import 'dart:io';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../core/network/api_endpoints.dart';

class SimpleImageUploadService {
  final Dio _dio;

  SimpleImageUploadService(this._dio);

  /// Subir imagen - Método súper simple
  /// 
  /// [file] - Archivo de imagen a subir
  /// [type] - Tipo de imagen: 'profile' o 'vehicle'
  /// 
  /// Retorna la URL de la imagen subida
  Future<String> uploadImage({
    required File file,
    required String type, // 'profile' o 'vehicle'
  }) async {
    try {
      print('📤 [SIMPLE-UPLOAD] Iniciando subida...');
      print('📁 [SIMPLE-UPLOAD] Archivo: ${file.path}');
      print('🖼️ [SIMPLE-UPLOAD] Tipo: $type');

      // Verificar que el archivo existe
      if (!await file.exists()) {
        throw Exception('El archivo no existe: ${file.path}');
      }

      // Obtener token de autenticación
      final token = await _getAccessToken();
      if (token == null) {
        throw Exception('No hay token de autenticación');
      }

      print('🔑 [SIMPLE-UPLOAD] Token obtenido');

      // Crear FormData
      final formData = FormData.fromMap({
        'image': await MultipartFile.fromFile(
          file.path,
          filename: file.path.split('/').last,
        ),
        'type': type,
      });

      print('📦 [SIMPLE-UPLOAD] FormData creado, enviando...');

      // Enviar al backend
      final response = await _dio.post(
        '/api/uploads/image',
        data: formData,
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'ngrok-skip-browser-warning': 'true',
          },
          sendTimeout: const Duration(seconds: 60),
          receiveTimeout: const Duration(seconds: 60),
        ),
      );

      print('✅ [SIMPLE-UPLOAD] Respuesta recibida: ${response.statusCode}');
      print('📄 [SIMPLE-UPLOAD] Data: ${response.data}');

      // Verificar respuesta
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data != null && response.data is Map) {
          if (response.data['success'] == true && response.data['url'] != null) {
            final imageUrl = response.data['url'] as String;
            print('🎉 [SIMPLE-UPLOAD] ¡Éxito! URL: $imageUrl');
            return imageUrl;
          } else {
            throw Exception('Respuesta del servidor sin URL: ${response.data}');
          }
        } else {
          throw Exception('Respuesta inválida del servidor');
        }
      } else {
        throw Exception('Error del servidor: ${response.statusCode}');
      }
    } on DioException catch (e) {
      print('❌ [SIMPLE-UPLOAD] Error de Dio: ${e.message}');
      print('❌ [SIMPLE-UPLOAD] Response: ${e.response?.data}');
      
      if (e.response?.statusCode == 401) {
        throw Exception('No autorizado. Verifica tu sesión');
      } else if (e.response?.statusCode == 400) {
        throw Exception('Archivo inválido o datos incorrectos');
      } else if (e.response?.statusCode == 503) {
        throw Exception('Servicio de almacenamiento no disponible');
      } else {
        throw Exception('Error de conexión: ${e.message}');
      }
    } catch (e) {
      print('❌ [SIMPLE-UPLOAD] Error inesperado: $e');
      throw Exception('Error inesperado: $e');
    }
  }

  /// Obtener token de acceso desde SharedPreferences
  Future<String?> _getAccessToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('access_token');
    } catch (e) {
      print('❌ [SIMPLE-UPLOAD] Error obteniendo token: $e');
      return null;
    }
  }
}
