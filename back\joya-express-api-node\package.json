{"name": "api-express", "version": "1.0.0", "description": "This is the application revolucionary to industry of mototaxi", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon src/server.js", "migrate": "sequelize-cli db:migrate", "undo": "sequelize-cli db:migrate:undo", "undo:all": "sequelize-cli db:migrate:undo:all", "migration": "sequelize-cli migration:generate --name", "seed": "sequelize-cli db:seed:all", "seed:undo": "sequelize-cli db:seed:undo:all", "start": "node src/server.js", "postdeploy": "npm run migrate"}, "repository": {"type": "git", "url": "**************-trabajo:Fabrica-de-Software-Tecsup/joya-express-api-node.git"}, "author": "sillyCombalistas", "license": "ISC", "dependencies": {"axios": "^1.9.0", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "firebase-admin": "^13.4.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "pg": "^8.16.0", "pg-hstore": "^2.3.4", "redis": "^5.1.0", "sequelize": "^6.37.7", "socket.io": "^4.8.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "twilio": "^5.6.1", "uuid": "^11.1.0"}, "devDependencies": {"nodemon": "^3.1.10", "sequelize-cli": "^6.6.3"}}