// Mocks generated by Mockito 5.4.4 from annotations
// in file_selector_windows/test/file_selector_windows_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:file_selector_windows/src/messages.g.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;

import 'test_api.g.dart' as _i3;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeFileDialogResult_0 extends _i1.SmartFake
    implements _i2.FileDialogResult {
  _FakeFileDialogResult_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [TestFileSelectorApi].
///
/// See the documentation for Mockito's code generation for more information.
class MockTestFileSelectorApi extends _i1.Mock
    implements _i3.TestFileSelectorApi {
  MockTestFileSelectorApi() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.FileDialogResult showOpenDialog(
    _i2.SelectionOptions? options,
    String? initialDirectory,
    String? confirmButtonText,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #showOpenDialog,
          [
            options,
            initialDirectory,
            confirmButtonText,
          ],
        ),
        returnValue: _FakeFileDialogResult_0(
          this,
          Invocation.method(
            #showOpenDialog,
            [
              options,
              initialDirectory,
              confirmButtonText,
            ],
          ),
        ),
      ) as _i2.FileDialogResult);

  @override
  _i2.FileDialogResult showSaveDialog(
    _i2.SelectionOptions? options,
    String? initialDirectory,
    String? suggestedName,
    String? confirmButtonText,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #showSaveDialog,
          [
            options,
            initialDirectory,
            suggestedName,
            confirmButtonText,
          ],
        ),
        returnValue: _FakeFileDialogResult_0(
          this,
          Invocation.method(
            #showSaveDialog,
            [
              options,
              initialDirectory,
              suggestedName,
              confirmButtonText,
            ],
          ),
        ),
      ) as _i2.FileDialogResult);
}
