{"buildFiles": ["C:\\dev\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "Z:\\programacion\\app sencilla\\frontend\\android\\app\\.cxx\\Debug\\5q3p693v\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "Z:\\programacion\\app sencilla\\frontend\\android\\app\\.cxx\\Debug\\5q3p693v\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}