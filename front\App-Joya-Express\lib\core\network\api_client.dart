import 'dart:io';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'api_endpoints.dart';
import 'api_exceptions.dart';

class ApiClient {
  static final ApiClient _instance = ApiClient._internal();
  factory ApiClient() => _instance;
  ApiClient._internal();
  final http.Client _client = http.Client();
  String? _accessToken;

  Future<void> saveTokens({
    required String accessToken,
    String? refreshToken,
  }) async {
    _accessToken = accessToken;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('access_token', accessToken);
    if (refreshToken != null) {
      await prefs.setString('refresh_token', refreshToken);
    }
  }

  /// Carga el token de refresco desde el almacenamiento.
  Future<String?> getRefreshToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('refresh_token');
  }

  /// Carga el token de acceso desde el almacenamiento.
  /// Útil al iniciar la app para ver si ya hay una sesión.
  Future<String?> loadAccessToken() async {
    if (_accessToken != null) return _accessToken;
    final prefs = await SharedPreferences.getInstance();
    _accessToken = prefs.getString('access_token');
    return _accessToken;
  }

  /// Limpia los tokens al cerrar sesión.
  Future<void> clearTokens() async {
    _accessToken = null;
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('access_token');
    await prefs.remove('refresh_token');
  }

  Future<Map<String, dynamic>> get(String endpoint) async {
    try {
      final headers = await _getHeaders();
      final response = await _client
          .get(Uri.parse('${ApiEndpoints.baseUrl}$endpoint'), headers: headers)
          .timeout(const Duration(seconds: 30));
      return _handleResponse(response);
    } catch (e) {
      throw _handleError(e);
    }
  }

  // POST Request con manejo de cookies
  Future<Map<String, dynamic>> post(
    String endpoint,
    Map<String, dynamic> body,
  ) async {
    try {
      final fullUrl = '${ApiEndpoints.baseUrl}$endpoint';
      print('🌐 [API_CLIENT] POST URL: $fullUrl');
      print('📦 [API_CLIENT] Body: $body');

      final headers = await _getHeaders();
      final response = await _client
          .post(
            Uri.parse(fullUrl),
            headers: headers,
            body: json.encode(body),
          )
          .timeout(const Duration(seconds: 30));

      return _handleResponse(response);
    } catch (e) {
      throw _handleError(e);
    }
  }

  // PUT Request con manejo de cookies
  Future<Map<String, dynamic>> put(
    String endpoint,
    Map<String, dynamic> body,
  ) async {
    try {
      final headers = await _getHeaders();
      final response = await _client
          .put(
            Uri.parse('${ApiEndpoints.baseUrl}$endpoint'),
            headers: headers,
            body: json.encode(body),
          )
          .timeout(const Duration(seconds: 30));

      return _handleResponse(response);
    } catch (e) {
      throw _handleError(e);
    }
  }

  // PATCH Request con manejo de cookies
  Future<Map<String, dynamic>> patch(
    String endpoint,
    Map<String, dynamic> body,
  ) async {
    try {
      final headers = await _getHeaders();
      final response = await _client
          .patch(
            Uri.parse('${ApiEndpoints.baseUrl}$endpoint'),
            headers: headers,
            body: json.encode(body),
          )
          .timeout(const Duration(seconds: 30));

      return _handleResponse(response);
    } catch (e) {
      throw _handleError(e);
    }
  }

  // DELETE Request con manejo de cookies
  Future<Map<String, dynamic>> delete(String endpoint) async {
    try {
      final headers = await _getHeaders();
      final response = await _client
          .delete(
            Uri.parse('${ApiEndpoints.baseUrl}$endpoint'),
            headers: headers,
          )
          .timeout(const Duration(seconds: 30));

      return _handleResponse(response);
    } catch (e) {
      throw _handleError(e);
    }
  }

  // Obtener headers con cookies de sesión
  Future<Map<String, String>> _getHeaders() async {
    final headers = Map<String, String>.from(ApiEndpoints.jsonHeaders);

    // Carga el token si aún no está en memoria
    await loadAccessToken();

    // Si tenemos un token, lo añadimos a la cabecera de Autorización.
    if (_accessToken != null) {
      headers['Authorization'] = 'Bearer $_accessToken';
    }
    return headers;
  }

  // Limpiar cookies de sesión

  Map<String, dynamic> _handleResponse(http.Response response) {
    final statusCode = response.statusCode;

    if (statusCode >= 200 && statusCode < 300) {
      try {
        if (response.body.isEmpty) {
          return {};
        }
        final decoded = json.decode(response.body);
        if (decoded is Map<String, dynamic>) {
          return decoded;
        } else {
          throw ApiException(
            message: 'Respuesta inesperada del servidor (no es un objeto JSON)',
            statusCode: statusCode,
          );
        }
      } catch (e) {
        throw ApiException(
          message: 'Error al procesar respuesta del servidor',
          statusCode: statusCode,
        );
      }
    } else {
      _handleHttpError(response);
    }

    throw ApiException(message: 'Respuesta inesperada del servidor');
  }

  // Manejar errores HTTP específicos
  void _handleHttpError(http.Response response) {
    final statusCode = response.statusCode;
    String message = 'Error desconocido';

    try {
      final errorBody = json.decode(response.body);
      message = errorBody['message'] ?? message;
    } catch (e) {
      // No hacer nada
    }

    switch (statusCode) {
      case 400:
        throw ValidationException(message: message);
      case 401:
        throw AuthException(message: message);
      case 403:
        throw AuthException(message: 'Acceso denegado');
      case 404:
        throw ApiException(message: 'Recurso no encontrado', statusCode: 404);
      case 409:
        throw ValidationException(message: message);
      case 500:
        throw ServerException(
          message: 'Error interno del servidor',
          statusCode: 500,
        );
      default:
        throw ApiException(message: message, statusCode: statusCode);
    }
  }

  // Manejar errores generales
  Exception _handleError(dynamic error) {
    if (error is ApiException) {
      return error;
    } else if (error is SocketException) {
      return NetworkException(message: 'Sin conexión a internet');
    } else if (error is HttpException) {
      return NetworkException(message: 'Error de red');
    } else {
      return ApiException(message: 'Error inesperado: ${error.toString()}');
    }
  }

  void dispose() {
    _client.close();
  }
}
