import 'dart:io';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:http/http.dart' as http;
import '../../core/network/api_client.dart';
import '../../core/network/api_exceptions.dart';

class DirectUploadService {
  final ApiClient _apiClient;

  DirectUploadService(this._apiClient);

  /// Subir imagen directamente al backend (que la sube a S3)
  Future<String> uploadImage({
    required File file,
    required String folder,
  }) async {
    try {
      print('🔄 [DIRECT-UPLOAD] Iniciando subida directa...');
      print('📁 [DIRECT-UPLOAD] Archivo: ${file.path}');
      print('📂 [DIRECT-UPLOAD] Carpeta: $folder');

      // Crear FormData
      final formData = FormData.fromMap({
        'image': await MultipartFile.fromFile(
          file.path,
          filename: file.path.split('/').last,
        ),
        'folder': folder,
      });

      print('📤 [DIRECT-UPLOAD] Enviando al backend...');

      // Enviar al backend
      final response = await _apiClient.post(
        '/uploads/direct',
        data: formData,
        options: Options(
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        ),
      );

      print('✅ [DIRECT-UPLOAD] Respuesta recibida');

      if (response.data['success'] == true) {
        final imageUrl = response.data['data']['imageUrl'] as String;
        print('🎉 [DIRECT-UPLOAD] ¡Subida exitosa! URL: $imageUrl');
        return imageUrl;
      } else {
        throw ServerException(
          message: response.data['message'] ?? 'Error subiendo imagen',
        );
      }
    } on DioException catch (e) {
      print('❌ [DIRECT-UPLOAD] Error de Dio: ${e.message}');
      
      if (e.response?.statusCode == 400) {
        throw ValidationException(
          message: e.response?.data['message'] ?? 'Archivo inválido',
        );
      } else if (e.response?.statusCode == 401) {
        throw AuthException(
          message: 'No autorizado. Verifica tu sesión',
        );
      } else if (e.response?.statusCode == 503) {
        throw ServiceUnavailableException(
          message: 'Servicio de almacenamiento no disponible',
        );
      }
      throw NetworkException(message: 'Error de conexión: ${e.message}');
    } catch (e) {
      print('❌ [DIRECT-UPLOAD] Error inesperado: $e');
      throw ApiException(message: 'Error inesperado: $e');
    }
  }

  /// Subir imagen desde bytes (para web)
  Future<String> uploadImageFromBytes({
    required Uint8List bytes,
    required String fileName,
    required String folder,
  }) async {
    try {
      print('🔄 [DIRECT-UPLOAD] Iniciando subida desde bytes...');
      print('📁 [DIRECT-UPLOAD] Archivo: $fileName');
      print('📂 [DIRECT-UPLOAD] Carpeta: $folder');

      // Crear FormData con bytes
      final formData = FormData.fromMap({
        'image': MultipartFile.fromBytes(
          bytes,
          filename: fileName,
        ),
        'folder': folder,
      });

      print('📤 [DIRECT-UPLOAD] Enviando al backend...');

      // Enviar al backend
      final response = await _apiClient.post(
        '/uploads/direct',
        data: formData,
        options: Options(
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        ),
      );

      print('✅ [DIRECT-UPLOAD] Respuesta recibida');

      if (response.data['success'] == true) {
        final imageUrl = response.data['data']['imageUrl'] as String;
        print('🎉 [DIRECT-UPLOAD] ¡Subida exitosa! URL: $imageUrl');
        return imageUrl;
      } else {
        throw ServerException(
          message: response.data['message'] ?? 'Error subiendo imagen',
        );
      }
    } on DioException catch (e) {
      print('❌ [DIRECT-UPLOAD] Error de Dio: ${e.message}');
      
      if (e.response?.statusCode == 400) {
        throw ValidationException(
          message: e.response?.data['message'] ?? 'Archivo inválido',
        );
      } else if (e.response?.statusCode == 401) {
        throw AuthException(
          message: 'No autorizado. Verifica tu sesión',
        );
      } else if (e.response?.statusCode == 503) {
        throw ServiceUnavailableException(
          message: 'Servicio de almacenamiento no disponible',
        );
      }
      throw NetworkException(message: 'Error de conexión: ${e.message}');
    } catch (e) {
      print('❌ [DIRECT-UPLOAD] Error inesperado: $e');
      throw ApiException(message: 'Error inesperado: $e');
    }
  }

  /// Eliminar imagen
  Future<bool> deleteImage(String key) async {
    try {
      print('🗑️ [DIRECT-UPLOAD] Eliminando imagen: $key');
      
      final response = await _apiClient.delete('/uploads/direct/$key');
      
      if (response.data['success'] == true) {
        print('✅ [DIRECT-UPLOAD] Imagen eliminada exitosamente');
        return true;
      }
      return false;
    } on DioException catch (e) {
      print('❌ [DIRECT-UPLOAD] Error eliminando: ${e.message}');
      
      if (e.response?.statusCode == 403) {
        throw UnauthorizedException(
          message: 'No tienes permisos para eliminar este archivo',
        );
      } else if (e.response?.statusCode == 404) {
        throw NotFoundException(message: 'Archivo no encontrado');
      }
      throw NetworkException(message: 'Error eliminando archivo: ${e.message}');
    } catch (e) {
      print('❌ [DIRECT-UPLOAD] Error inesperado eliminando: $e');
      throw ApiException(message: 'Error inesperado: $e');
    }
  }
}
