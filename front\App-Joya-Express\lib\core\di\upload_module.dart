import 'package:get_it/get_it.dart';
import '../../data/services/upload_service.dart';
import '../../presentation/providers/upload_provider.dart';
import '../network/api_client.dart';

/// Módulo de configuración para servicios de upload
class UploadModule {
  static void configure(GetIt getIt) {
    // Registrar UploadService (URLs firmadas)
    getIt.registerLazySingleton<UploadService>(
      () => UploadService(getIt<ApiClient>()),
    );

    // FileUploadService ya está registrado en service_locator.dart
    // No lo registramos aquí para evitar duplicados

    // Registrar UploadProvider como factory para crear nuevas instancias
    getIt.registerFactory<UploadProvider>(
      () => UploadProvider(getIt<UploadService>()),
    );
  }
}
