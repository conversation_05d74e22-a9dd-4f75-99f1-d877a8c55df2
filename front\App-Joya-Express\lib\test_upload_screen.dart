import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:joya_express/core/di/injection_container.dart';
import 'package:joya_express/data/services/upload_service.dart';

class TestUploadScreen extends StatefulWidget {
  const TestUploadScreen({super.key});

  @override
  State<TestUploadScreen> createState() => _TestUploadScreenState();
}

class _TestUploadScreenState extends State<TestUploadScreen> {
  final UploadService _uploadService = getIt<UploadService>();
  final ImagePicker _picker = ImagePicker();
  
  bool _isUploading = false;
  String? _result;
  String? _error;
  File? _selectedImage;

  Future<void> _pickAndTestUpload() async {
    try {
      // 1. Seleccionar imagen
      final XFile? pickedFile = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (pickedFile == null) return;

      setState(() {
        _selectedImage = File(pickedFile.path);
        _isUploading = true;
        _result = null;
        _error = null;
      });

      print('🔄 [TEST] Iniciando prueba de subida...');
      print('📁 [TEST] Archivo seleccionado: ${pickedFile.path}');

      // 2. Probar subida completa
      final imageUrl = await _uploadService.uploadImage(
        file: File(pickedFile.path),
        folder: 'profile',
      );

      setState(() {
        _result = imageUrl;
        _isUploading = false;
      });

      print('🎉 [TEST] ¡Subida exitosa! URL: $imageUrl');

    } catch (e) {
      setState(() {
        _error = e.toString();
        _isUploading = false;
      });

      print('❌ [TEST] Error en la subida: $e');
    }
  }

  Future<void> _testGenerateUrl() async {
    try {
      setState(() {
        _isUploading = true;
        _result = null;
        _error = null;
      });

      print('🔗 [TEST] Probando solo generación de URL...');

      final uploadData = await _uploadService.generateUploadUrl(
        fileType: 'image/jpeg',
        folder: 'profile',
      );

      setState(() {
        _result = 'URL generada exitosamente:\n'
            'Upload URL: ${uploadData.uploadUrl.substring(0, 100)}...\n'
            'Public URL: ${uploadData.publicUrl}\n'
            'Key: ${uploadData.key}';
        _isUploading = false;
      });

      print('✅ [TEST] URL generada exitosamente');

    } catch (e) {
      setState(() {
        _error = e.toString();
        _isUploading = false;
      });

      print('❌ [TEST] Error generando URL: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Upload S3'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            if (_selectedImage != null) ...[
              Container(
                height: 200,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Image.file(
                  _selectedImage!,
                  fit: BoxFit.cover,
                ),
              ),
              const SizedBox(height: 16),
            ],
            
            ElevatedButton(
              onPressed: _isUploading ? null : _testGenerateUrl,
              child: _isUploading 
                ? const CircularProgressIndicator()
                : const Text('1. Probar Solo Generar URL'),
            ),
            
            const SizedBox(height: 16),
            
            ElevatedButton(
              onPressed: _isUploading ? null : _pickAndTestUpload,
              child: _isUploading 
                ? const CircularProgressIndicator()
                : const Text('2. Probar Subida Completa'),
            ),
            
            const SizedBox(height: 24),
            
            if (_result != null) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  border: Border.all(color: Colors.green),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '✅ ÉXITO:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(_result!),
                  ],
                ),
              ),
            ],
            
            if (_error != null) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  border: Border.all(color: Colors.red),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '❌ ERROR:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.red,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(_error!),
                  ],
                ),
              ),
            ],
            
            const SizedBox(height: 24),
            
            const Text(
              'Instrucciones:\n'
              '1. Primero prueba "Generar URL" para verificar la conexión con el backend\n'
              '2. Luego prueba "Subida Completa" para el flujo completo\n'
              '3. Revisa los logs en la consola para más detalles',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
