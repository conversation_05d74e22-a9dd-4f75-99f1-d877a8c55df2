# geolocator_linux

[![pub package](https://img.shields.io/pub/v/geolocator.svg)](https://pub.dartlang.org/packages/geolocator) ![Build status](https://github.com/Baseflow/flutter-geolocator/workflows/geolocator_linux/badge.svg?branch=master) [![style: effective dart](https://img.shields.io/badge/style-effective_dart-40c4ff.svg)](https://github.com/tenhobi/effective_dart)

The official Linux implementation of the [geolocator](https://pub.dev/packages/geolocator) plugin by [Baseflow](https://baseflow.com).

## Usage

The [geolocator_linux](https://pub.dev/packages/geolocator_linux) package is currently available as beta version and therefore not yet fully endorsed by the [geolocator](https://pub.dev/packages/geolocator) package.

To add Linux support to the [geolocator](https://pub.dev/packages/geolocator) simply add the [geolocator_linux](https://pub.dev/packages/geolocator_linux) package as an additional dependency to the `pubspec.yaml` file of your app:

```yaml
dependencies:
  ...
  geolocator: ^8.2.0
  geolocator_linux: ^0.1.0
```

## Issues

Please file any issues, bugs or feature requests as an issue on our [GitHub](https://github.com/Baseflow/flutter-geolocator/issues) page. Commercial support is available, you can contact us at <<EMAIL>>.

## Want to contribute

If you would like to contribute to the plugin (e.g. by improving the documentation, solving a bug or adding a cool new feature), please carefully review our [contribution guide](../CONTRIBUTING.md) and send us your [pull request](https://github.com/Baseflow/flutter-geolocator/pulls).

## Author

This Geolocator plugin for Flutter is developed by [Baseflow](https://baseflow.com).
