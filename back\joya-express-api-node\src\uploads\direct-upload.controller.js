const AWS = require('aws-sdk');
const multer = require('multer');
const { v4: uuidv4 } = require('uuid');

// Configurar AWS S3
const s3 = new AWS.S3({
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    region: process.env.S3_REGION || 'us-east-1',
    signatureVersion: 'v4'
});

// Configurar multer para manejar archivos en memoria
const upload = multer({
    storage: multer.memoryStorage(),
    limits: {
        fileSize: 10 * 1024 * 1024, // 10MB máximo
    },
    fileFilter: (req, file, cb) => {
        // Validar tipos de archivo
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
        if (allowedTypes.includes(file.mimetype)) {
            cb(null, true);
        } else {
            cb(new Error('Tipo de archivo no permitido. Solo se permiten JPEG, PNG y WebP'), false);
        }
    }
});

class DirectUploadController {
    /**
     * Middleware de multer para procesar archivos
     */
    static getUploadMiddleware() {
        return upload.single('image');
    }

    /**
     * Subir imagen directamente a S3
     */
    async uploadImage(req, res) {
        try {
            console.log('📤 [DIRECT-UPLOAD] Iniciando subida directa...');
            
            if (!req.file) {
                return res.status(400).json({
                    success: false,
                    message: 'No se proporcionó ninguna imagen'
                });
            }

            // Validar que S3 esté configurado
            if (!process.env.S3_BUCKET_NAME) {
                return res.status(503).json({
                    success: false,
                    message: 'Servicio de almacenamiento no configurado'
                });
            }

            const file = req.file;
            const userId = req.user?.id || 'anonymous';
            const userType = req.user?.tipo || 'usuario';
            const folder = req.body.folder || 'general';

            console.log('📁 [DIRECT-UPLOAD] Archivo:', file.originalname);
            console.log('👤 [DIRECT-UPLOAD] Usuario:', userId);
            console.log('📂 [DIRECT-UPLOAD] Carpeta:', folder);

            // Mapear carpetas según el tipo de usuario
            const folderMap = {
                'profile': userType === 'conductor' ? 'conductores/perfiles' : 'usuarios/perfiles',
                'vehicle': 'vehiculos',
                'document': 'conductores/documentos',
                'general': 'general'
            };

            const s3Folder = folderMap[folder] || 'general';
            
            // Generar nombre único para el archivo
            const fileExtension = this.getFileExtension(file.mimetype);
            const fileName = `${uuidv4()}${fileExtension}`;
            const key = `${s3Folder}/${userId}/${fileName}`;

            console.log('🔑 [DIRECT-UPLOAD] Key S3:', key);

            // Parámetros para S3
            const params = {
                Bucket: process.env.S3_BUCKET_NAME,
                Key: key,
                Body: file.buffer,
                ContentType: file.mimetype,
                ACL: 'public-read'
            };

            // Subir a S3
            console.log('☁️ [DIRECT-UPLOAD] Subiendo a S3...');
            const result = await s3.upload(params).promise();
            
            console.log('✅ [DIRECT-UPLOAD] Imagen subida exitosamente:', result.Location);
            
            res.json({
                success: true,
                data: {
                    imageUrl: result.Location,
                    key: key,
                    fileName: fileName,
                    originalName: file.originalname,
                    size: file.size,
                    contentType: file.mimetype
                },
                message: 'Imagen subida exitosamente a S3'
            });

        } catch (error) {
            console.error('❌ [DIRECT-UPLOAD] Error al subir imagen:', error);
            
            if (error.message.includes('Tipo de archivo no permitido')) {
                return res.status(400).json({
                    success: false,
                    message: error.message,
                    allowedTypes: ['image/jpeg', 'image/png', 'image/webp']
                });
            }

            res.status(500).json({
                success: false,
                message: 'Error al subir la imagen',
                error: error.message
            });
        }
    }

    /**
     * Obtener extensión de archivo basada en el tipo MIME
     */
    getFileExtension(mimeType) {
        const extensions = {
            'image/jpeg': '.jpg',
            'image/jpg': '.jpg',
            'image/png': '.png',
            'image/webp': '.webp'
        };
        return extensions[mimeType] || '.jpg';
    }

    /**
     * Eliminar imagen de S3
     */
    async deleteImage(req, res) {
        try {
            const { key } = req.params;
            
            if (!key) {
                return res.status(400).json({
                    success: false,
                    message: 'Key de archivo requerido'
                });
            }

            const params = {
                Bucket: process.env.S3_BUCKET_NAME,
                Key: key
            };

            await s3.deleteObject(params).promise();
            
            console.log('🗑️ [DIRECT-UPLOAD] Imagen eliminada:', key);
            
            res.json({
                success: true,
                message: 'Imagen eliminada exitosamente'
            });

        } catch (error) {
            console.error('❌ [DIRECT-UPLOAD] Error eliminando imagen:', error);
            res.status(500).json({
                success: false,
                message: 'Error eliminando imagen',
                error: error.message
            });
        }
    }
}

module.exports = new DirectUploadController();
