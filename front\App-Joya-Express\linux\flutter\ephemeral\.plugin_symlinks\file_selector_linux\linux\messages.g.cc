// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.
// Autogenerated from Pig<PERSON> (v22.6.2), do not edit directly.
// See also: https://pub.dev/packages/pigeon

#include "messages.g.h"

struct _FfsPlatformTypeGroup {
  GObject parent_instance;

  gchar* label;
  FlValue* extensions;
  FlValue* mime_types;
};

G_DEFINE_TYPE(FfsPlatformTypeGroup, ffs_platform_type_group, G_TYPE_OBJECT)

static void ffs_platform_type_group_dispose(GObject* object) {
  FfsPlatformTypeGroup* self = FFS_PLATFORM_TYPE_GROUP(object);
  g_clear_pointer(&self->label, g_free);
  g_clear_pointer(&self->extensions, fl_value_unref);
  g_clear_pointer(&self->mime_types, fl_value_unref);
  G_OBJECT_CLASS(ffs_platform_type_group_parent_class)->dispose(object);
}

static void ffs_platform_type_group_init(FfsPlatformTypeGroup* self) {}

static void ffs_platform_type_group_class_init(
    FfsPlatformTypeGroupClass* klass) {
  G_OBJECT_CLASS(klass)->dispose = ffs_platform_type_group_dispose;
}

FfsPlatformTypeGroup* ffs_platform_type_group_new(const gchar* label,
                                                  FlValue* extensions,
                                                  FlValue* mime_types) {
  FfsPlatformTypeGroup* self = FFS_PLATFORM_TYPE_GROUP(
      g_object_new(ffs_platform_type_group_get_type(), nullptr));
  self->label = g_strdup(label);
  self->extensions = fl_value_ref(extensions);
  self->mime_types = fl_value_ref(mime_types);
  return self;
}

const gchar* ffs_platform_type_group_get_label(FfsPlatformTypeGroup* self) {
  g_return_val_if_fail(FFS_IS_PLATFORM_TYPE_GROUP(self), nullptr);
  return self->label;
}

FlValue* ffs_platform_type_group_get_extensions(FfsPlatformTypeGroup* self) {
  g_return_val_if_fail(FFS_IS_PLATFORM_TYPE_GROUP(self), nullptr);
  return self->extensions;
}

FlValue* ffs_platform_type_group_get_mime_types(FfsPlatformTypeGroup* self) {
  g_return_val_if_fail(FFS_IS_PLATFORM_TYPE_GROUP(self), nullptr);
  return self->mime_types;
}

static FlValue* ffs_platform_type_group_to_list(FfsPlatformTypeGroup* self) {
  FlValue* values = fl_value_new_list();
  fl_value_append_take(values, fl_value_new_string(self->label));
  fl_value_append_take(values, fl_value_ref(self->extensions));
  fl_value_append_take(values, fl_value_ref(self->mime_types));
  return values;
}

static FfsPlatformTypeGroup* ffs_platform_type_group_new_from_list(
    FlValue* values) {
  FlValue* value0 = fl_value_get_list_value(values, 0);
  const gchar* label = fl_value_get_string(value0);
  FlValue* value1 = fl_value_get_list_value(values, 1);
  FlValue* extensions = value1;
  FlValue* value2 = fl_value_get_list_value(values, 2);
  FlValue* mime_types = value2;
  return ffs_platform_type_group_new(label, extensions, mime_types);
}

struct _FfsPlatformFileChooserOptions {
  GObject parent_instance;

  FlValue* allowed_file_types;
  gchar* current_folder_path;
  gchar* current_name;
  gchar* accept_button_label;
  gboolean* select_multiple;
};

G_DEFINE_TYPE(FfsPlatformFileChooserOptions, ffs_platform_file_chooser_options,
              G_TYPE_OBJECT)

static void ffs_platform_file_chooser_options_dispose(GObject* object) {
  FfsPlatformFileChooserOptions* self =
      FFS_PLATFORM_FILE_CHOOSER_OPTIONS(object);
  g_clear_pointer(&self->allowed_file_types, fl_value_unref);
  g_clear_pointer(&self->current_folder_path, g_free);
  g_clear_pointer(&self->current_name, g_free);
  g_clear_pointer(&self->accept_button_label, g_free);
  g_clear_pointer(&self->select_multiple, g_free);
  G_OBJECT_CLASS(ffs_platform_file_chooser_options_parent_class)
      ->dispose(object);
}

static void ffs_platform_file_chooser_options_init(
    FfsPlatformFileChooserOptions* self) {}

static void ffs_platform_file_chooser_options_class_init(
    FfsPlatformFileChooserOptionsClass* klass) {
  G_OBJECT_CLASS(klass)->dispose = ffs_platform_file_chooser_options_dispose;
}

FfsPlatformFileChooserOptions* ffs_platform_file_chooser_options_new(
    FlValue* allowed_file_types, const gchar* current_folder_path,
    const gchar* current_name, const gchar* accept_button_label,
    gboolean* select_multiple) {
  FfsPlatformFileChooserOptions* self = FFS_PLATFORM_FILE_CHOOSER_OPTIONS(
      g_object_new(ffs_platform_file_chooser_options_get_type(), nullptr));
  if (allowed_file_types != nullptr) {
    self->allowed_file_types = fl_value_ref(allowed_file_types);
  } else {
    self->allowed_file_types = nullptr;
  }
  if (current_folder_path != nullptr) {
    self->current_folder_path = g_strdup(current_folder_path);
  } else {
    self->current_folder_path = nullptr;
  }
  if (current_name != nullptr) {
    self->current_name = g_strdup(current_name);
  } else {
    self->current_name = nullptr;
  }
  if (accept_button_label != nullptr) {
    self->accept_button_label = g_strdup(accept_button_label);
  } else {
    self->accept_button_label = nullptr;
  }
  if (select_multiple != nullptr) {
    self->select_multiple = static_cast<gboolean*>(malloc(sizeof(gboolean)));
    *self->select_multiple = *select_multiple;
  } else {
    self->select_multiple = nullptr;
  }
  return self;
}

FlValue* ffs_platform_file_chooser_options_get_allowed_file_types(
    FfsPlatformFileChooserOptions* self) {
  g_return_val_if_fail(FFS_IS_PLATFORM_FILE_CHOOSER_OPTIONS(self), nullptr);
  return self->allowed_file_types;
}

const gchar* ffs_platform_file_chooser_options_get_current_folder_path(
    FfsPlatformFileChooserOptions* self) {
  g_return_val_if_fail(FFS_IS_PLATFORM_FILE_CHOOSER_OPTIONS(self), nullptr);
  return self->current_folder_path;
}

const gchar* ffs_platform_file_chooser_options_get_current_name(
    FfsPlatformFileChooserOptions* self) {
  g_return_val_if_fail(FFS_IS_PLATFORM_FILE_CHOOSER_OPTIONS(self), nullptr);
  return self->current_name;
}

const gchar* ffs_platform_file_chooser_options_get_accept_button_label(
    FfsPlatformFileChooserOptions* self) {
  g_return_val_if_fail(FFS_IS_PLATFORM_FILE_CHOOSER_OPTIONS(self), nullptr);
  return self->accept_button_label;
}

gboolean* ffs_platform_file_chooser_options_get_select_multiple(
    FfsPlatformFileChooserOptions* self) {
  g_return_val_if_fail(FFS_IS_PLATFORM_FILE_CHOOSER_OPTIONS(self), nullptr);
  return self->select_multiple;
}

static FlValue* ffs_platform_file_chooser_options_to_list(
    FfsPlatformFileChooserOptions* self) {
  FlValue* values = fl_value_new_list();
  fl_value_append_take(values, self->allowed_file_types != nullptr
                                   ? fl_value_ref(self->allowed_file_types)
                                   : fl_value_new_null());
  fl_value_append_take(values,
                       self->current_folder_path != nullptr
                           ? fl_value_new_string(self->current_folder_path)
                           : fl_value_new_null());
  fl_value_append_take(values, self->current_name != nullptr
                                   ? fl_value_new_string(self->current_name)
                                   : fl_value_new_null());
  fl_value_append_take(values,
                       self->accept_button_label != nullptr
                           ? fl_value_new_string(self->accept_button_label)
                           : fl_value_new_null());
  fl_value_append_take(values, self->select_multiple != nullptr
                                   ? fl_value_new_bool(*self->select_multiple)
                                   : fl_value_new_null());
  return values;
}

static FfsPlatformFileChooserOptions*
ffs_platform_file_chooser_options_new_from_list(FlValue* values) {
  FlValue* value0 = fl_value_get_list_value(values, 0);
  FlValue* allowed_file_types = nullptr;
  if (fl_value_get_type(value0) != FL_VALUE_TYPE_NULL) {
    allowed_file_types = value0;
  }
  FlValue* value1 = fl_value_get_list_value(values, 1);
  const gchar* current_folder_path = nullptr;
  if (fl_value_get_type(value1) != FL_VALUE_TYPE_NULL) {
    current_folder_path = fl_value_get_string(value1);
  }
  FlValue* value2 = fl_value_get_list_value(values, 2);
  const gchar* current_name = nullptr;
  if (fl_value_get_type(value2) != FL_VALUE_TYPE_NULL) {
    current_name = fl_value_get_string(value2);
  }
  FlValue* value3 = fl_value_get_list_value(values, 3);
  const gchar* accept_button_label = nullptr;
  if (fl_value_get_type(value3) != FL_VALUE_TYPE_NULL) {
    accept_button_label = fl_value_get_string(value3);
  }
  FlValue* value4 = fl_value_get_list_value(values, 4);
  gboolean* select_multiple = nullptr;
  gboolean select_multiple_value;
  if (fl_value_get_type(value4) != FL_VALUE_TYPE_NULL) {
    select_multiple_value = fl_value_get_bool(value4);
    select_multiple = &select_multiple_value;
  }
  return ffs_platform_file_chooser_options_new(
      allowed_file_types, current_folder_path, current_name,
      accept_button_label, select_multiple);
}

struct _FfsMessageCodec {
  FlStandardMessageCodec parent_instance;
};

G_DEFINE_TYPE(FfsMessageCodec, ffs_message_codec,
              fl_standard_message_codec_get_type())

static gboolean ffs_message_codec_write_ffs_platform_file_chooser_action_type(
    FlStandardMessageCodec* codec, GByteArray* buffer, FlValue* value,
    GError** error) {
  uint8_t type = 129;
  g_byte_array_append(buffer, &type, sizeof(uint8_t));
  return fl_standard_message_codec_write_value(codec, buffer, value, error);
}

static gboolean ffs_message_codec_write_ffs_platform_type_group(
    FlStandardMessageCodec* codec, GByteArray* buffer,
    FfsPlatformTypeGroup* value, GError** error) {
  uint8_t type = 130;
  g_byte_array_append(buffer, &type, sizeof(uint8_t));
  g_autoptr(FlValue) values = ffs_platform_type_group_to_list(value);
  return fl_standard_message_codec_write_value(codec, buffer, values, error);
}

static gboolean ffs_message_codec_write_ffs_platform_file_chooser_options(
    FlStandardMessageCodec* codec, GByteArray* buffer,
    FfsPlatformFileChooserOptions* value, GError** error) {
  uint8_t type = 131;
  g_byte_array_append(buffer, &type, sizeof(uint8_t));
  g_autoptr(FlValue) values = ffs_platform_file_chooser_options_to_list(value);
  return fl_standard_message_codec_write_value(codec, buffer, values, error);
}

static gboolean ffs_message_codec_write_value(FlStandardMessageCodec* codec,
                                              GByteArray* buffer,
                                              FlValue* value, GError** error) {
  if (fl_value_get_type(value) == FL_VALUE_TYPE_CUSTOM) {
    switch (fl_value_get_custom_type(value)) {
      case 129:
        return ffs_message_codec_write_ffs_platform_file_chooser_action_type(
            codec, buffer,
            reinterpret_cast<FlValue*>(
                const_cast<gpointer>(fl_value_get_custom_value(value))),
            error);
      case 130:
        return ffs_message_codec_write_ffs_platform_type_group(
            codec, buffer,
            FFS_PLATFORM_TYPE_GROUP(fl_value_get_custom_value_object(value)),
            error);
      case 131:
        return ffs_message_codec_write_ffs_platform_file_chooser_options(
            codec, buffer,
            FFS_PLATFORM_FILE_CHOOSER_OPTIONS(
                fl_value_get_custom_value_object(value)),
            error);
    }
  }

  return FL_STANDARD_MESSAGE_CODEC_CLASS(ffs_message_codec_parent_class)
      ->write_value(codec, buffer, value, error);
}

static FlValue* ffs_message_codec_read_ffs_platform_file_chooser_action_type(
    FlStandardMessageCodec* codec, GBytes* buffer, size_t* offset,
    GError** error) {
  return fl_value_new_custom(
      129, fl_standard_message_codec_read_value(codec, buffer, offset, error),
      (GDestroyNotify)fl_value_unref);
}

static FlValue* ffs_message_codec_read_ffs_platform_type_group(
    FlStandardMessageCodec* codec, GBytes* buffer, size_t* offset,
    GError** error) {
  g_autoptr(FlValue) values =
      fl_standard_message_codec_read_value(codec, buffer, offset, error);
  if (values == nullptr) {
    return nullptr;
  }

  g_autoptr(FfsPlatformTypeGroup) value =
      ffs_platform_type_group_new_from_list(values);
  if (value == nullptr) {
    g_set_error(error, FL_MESSAGE_CODEC_ERROR, FL_MESSAGE_CODEC_ERROR_FAILED,
                "Invalid data received for MessageData");
    return nullptr;
  }

  return fl_value_new_custom_object(130, G_OBJECT(value));
}

static FlValue* ffs_message_codec_read_ffs_platform_file_chooser_options(
    FlStandardMessageCodec* codec, GBytes* buffer, size_t* offset,
    GError** error) {
  g_autoptr(FlValue) values =
      fl_standard_message_codec_read_value(codec, buffer, offset, error);
  if (values == nullptr) {
    return nullptr;
  }

  g_autoptr(FfsPlatformFileChooserOptions) value =
      ffs_platform_file_chooser_options_new_from_list(values);
  if (value == nullptr) {
    g_set_error(error, FL_MESSAGE_CODEC_ERROR, FL_MESSAGE_CODEC_ERROR_FAILED,
                "Invalid data received for MessageData");
    return nullptr;
  }

  return fl_value_new_custom_object(131, G_OBJECT(value));
}

static FlValue* ffs_message_codec_read_value_of_type(
    FlStandardMessageCodec* codec, GBytes* buffer, size_t* offset, int type,
    GError** error) {
  switch (type) {
    case 129:
      return ffs_message_codec_read_ffs_platform_file_chooser_action_type(
          codec, buffer, offset, error);
    case 130:
      return ffs_message_codec_read_ffs_platform_type_group(codec, buffer,
                                                            offset, error);
    case 131:
      return ffs_message_codec_read_ffs_platform_file_chooser_options(
          codec, buffer, offset, error);
    default:
      return FL_STANDARD_MESSAGE_CODEC_CLASS(ffs_message_codec_parent_class)
          ->read_value_of_type(codec, buffer, offset, type, error);
  }
}

static void ffs_message_codec_init(FfsMessageCodec* self) {}

static void ffs_message_codec_class_init(FfsMessageCodecClass* klass) {
  FL_STANDARD_MESSAGE_CODEC_CLASS(klass)->write_value =
      ffs_message_codec_write_value;
  FL_STANDARD_MESSAGE_CODEC_CLASS(klass)->read_value_of_type =
      ffs_message_codec_read_value_of_type;
}

static FfsMessageCodec* ffs_message_codec_new() {
  FfsMessageCodec* self =
      FFS_MESSAGE_CODEC(g_object_new(ffs_message_codec_get_type(), nullptr));
  return self;
}

struct _FfsFileSelectorApiShowFileChooserResponse {
  GObject parent_instance;

  FlValue* value;
};

G_DEFINE_TYPE(FfsFileSelectorApiShowFileChooserResponse,
              ffs_file_selector_api_show_file_chooser_response, G_TYPE_OBJECT)

static void ffs_file_selector_api_show_file_chooser_response_dispose(
    GObject* object) {
  FfsFileSelectorApiShowFileChooserResponse* self =
      FFS_FILE_SELECTOR_API_SHOW_FILE_CHOOSER_RESPONSE(object);
  g_clear_pointer(&self->value, fl_value_unref);
  G_OBJECT_CLASS(ffs_file_selector_api_show_file_chooser_response_parent_class)
      ->dispose(object);
}

static void ffs_file_selector_api_show_file_chooser_response_init(
    FfsFileSelectorApiShowFileChooserResponse* self) {}

static void ffs_file_selector_api_show_file_chooser_response_class_init(
    FfsFileSelectorApiShowFileChooserResponseClass* klass) {
  G_OBJECT_CLASS(klass)->dispose =
      ffs_file_selector_api_show_file_chooser_response_dispose;
}

FfsFileSelectorApiShowFileChooserResponse*
ffs_file_selector_api_show_file_chooser_response_new(FlValue* return_value) {
  FfsFileSelectorApiShowFileChooserResponse* self =
      FFS_FILE_SELECTOR_API_SHOW_FILE_CHOOSER_RESPONSE(g_object_new(
          ffs_file_selector_api_show_file_chooser_response_get_type(),
          nullptr));
  self->value = fl_value_new_list();
  fl_value_append_take(self->value, fl_value_ref(return_value));
  return self;
}

FfsFileSelectorApiShowFileChooserResponse*
ffs_file_selector_api_show_file_chooser_response_new_error(const gchar* code,
                                                           const gchar* message,
                                                           FlValue* details) {
  FfsFileSelectorApiShowFileChooserResponse* self =
      FFS_FILE_SELECTOR_API_SHOW_FILE_CHOOSER_RESPONSE(g_object_new(
          ffs_file_selector_api_show_file_chooser_response_get_type(),
          nullptr));
  self->value = fl_value_new_list();
  fl_value_append_take(self->value, fl_value_new_string(code));
  fl_value_append_take(self->value,
                       fl_value_new_string(message != nullptr ? message : ""));
  fl_value_append_take(self->value, details != nullptr ? fl_value_ref(details)
                                                       : fl_value_new_null());
  return self;
}

struct _FfsFileSelectorApi {
  GObject parent_instance;

  const FfsFileSelectorApiVTable* vtable;
  gpointer user_data;
  GDestroyNotify user_data_free_func;
};

G_DEFINE_TYPE(FfsFileSelectorApi, ffs_file_selector_api, G_TYPE_OBJECT)

static void ffs_file_selector_api_dispose(GObject* object) {
  FfsFileSelectorApi* self = FFS_FILE_SELECTOR_API(object);
  if (self->user_data != nullptr) {
    self->user_data_free_func(self->user_data);
  }
  self->user_data = nullptr;
  G_OBJECT_CLASS(ffs_file_selector_api_parent_class)->dispose(object);
}

static void ffs_file_selector_api_init(FfsFileSelectorApi* self) {}

static void ffs_file_selector_api_class_init(FfsFileSelectorApiClass* klass) {
  G_OBJECT_CLASS(klass)->dispose = ffs_file_selector_api_dispose;
}

static FfsFileSelectorApi* ffs_file_selector_api_new(
    const FfsFileSelectorApiVTable* vtable, gpointer user_data,
    GDestroyNotify user_data_free_func) {
  FfsFileSelectorApi* self = FFS_FILE_SELECTOR_API(
      g_object_new(ffs_file_selector_api_get_type(), nullptr));
  self->vtable = vtable;
  self->user_data = user_data;
  self->user_data_free_func = user_data_free_func;
  return self;
}

static void ffs_file_selector_api_show_file_chooser_cb(
    FlBasicMessageChannel* channel, FlValue* message_,
    FlBasicMessageChannelResponseHandle* response_handle, gpointer user_data) {
  FfsFileSelectorApi* self = FFS_FILE_SELECTOR_API(user_data);

  if (self->vtable == nullptr || self->vtable->show_file_chooser == nullptr) {
    return;
  }

  FlValue* value0 = fl_value_get_list_value(message_, 0);
  FfsPlatformFileChooserActionType type =
      static_cast<FfsPlatformFileChooserActionType>(
          fl_value_get_int(reinterpret_cast<FlValue*>(
              const_cast<gpointer>(fl_value_get_custom_value(value0)))));
  FlValue* value1 = fl_value_get_list_value(message_, 1);
  FfsPlatformFileChooserOptions* options = FFS_PLATFORM_FILE_CHOOSER_OPTIONS(
      fl_value_get_custom_value_object(value1));
  g_autoptr(FfsFileSelectorApiShowFileChooserResponse) response =
      self->vtable->show_file_chooser(type, options, self->user_data);
  if (response == nullptr) {
    g_warning("No response returned to %s.%s", "FileSelectorApi",
              "showFileChooser");
    return;
  }

  g_autoptr(GError) error = NULL;
  if (!fl_basic_message_channel_respond(channel, response_handle,
                                        response->value, &error)) {
    g_warning("Failed to send response to %s.%s: %s", "FileSelectorApi",
              "showFileChooser", error->message);
  }
}

void ffs_file_selector_api_set_method_handlers(
    FlBinaryMessenger* messenger, const gchar* suffix,
    const FfsFileSelectorApiVTable* vtable, gpointer user_data,
    GDestroyNotify user_data_free_func) {
  g_autofree gchar* dot_suffix =
      suffix != nullptr ? g_strdup_printf(".%s", suffix) : g_strdup("");
  g_autoptr(FfsFileSelectorApi) api_data =
      ffs_file_selector_api_new(vtable, user_data, user_data_free_func);

  g_autoptr(FfsMessageCodec) codec = ffs_message_codec_new();
  g_autofree gchar* show_file_chooser_channel_name = g_strdup_printf(
      "dev.flutter.pigeon.file_selector_linux.FileSelectorApi.showFileChooser%"
      "s",
      dot_suffix);
  g_autoptr(FlBasicMessageChannel) show_file_chooser_channel =
      fl_basic_message_channel_new(messenger, show_file_chooser_channel_name,
                                   FL_MESSAGE_CODEC(codec));
  fl_basic_message_channel_set_message_handler(
      show_file_chooser_channel, ffs_file_selector_api_show_file_chooser_cb,
      g_object_ref(api_data), g_object_unref);
}

void ffs_file_selector_api_clear_method_handlers(FlBinaryMessenger* messenger,
                                                 const gchar* suffix) {
  g_autofree gchar* dot_suffix =
      suffix != nullptr ? g_strdup_printf(".%s", suffix) : g_strdup("");

  g_autoptr(FfsMessageCodec) codec = ffs_message_codec_new();
  g_autofree gchar* show_file_chooser_channel_name = g_strdup_printf(
      "dev.flutter.pigeon.file_selector_linux.FileSelectorApi.showFileChooser%"
      "s",
      dot_suffix);
  g_autoptr(FlBasicMessageChannel) show_file_chooser_channel =
      fl_basic_message_channel_new(messenger, show_file_chooser_channel_name,
                                   FL_MESSAGE_CODEC(codec));
  fl_basic_message_channel_set_message_handler(show_file_chooser_channel,
                                               nullptr, nullptr, nullptr);
}
