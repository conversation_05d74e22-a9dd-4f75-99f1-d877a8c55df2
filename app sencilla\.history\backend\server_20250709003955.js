const express = require('express');
const cors = require('cors');
const AWS = require('aws-sdk');
const multer = require('multer');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Configuración de CORS para permitir conexiones desde Flutter
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Configuración de AWS S3
const s3 = new AWS.S3({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION
});

// Configuración de Multer para manejo de archivos
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB máximo
  }
});

// Ruta de prueba
app.get('/', (req, res) => {
  res.json({ 
    message: 'Backend funcionando correctamente',
    status: 'OK',
    timestamp: new Date().toISOString()
  });
});

// Ruta para subir imagen a S3
app.post('/upload', upload.single('image'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No se proporcionó ninguna imagen' });
    }

    const file = req.file;
    const fileName = `images/${Date.now()}-${file.originalname}`;

    const params = {
      Bucket: process.env.S3_BUCKET_NAME,
      Key: fileName,
      Body: file.buffer,
      ContentType: file.mimetype,
      //ACL: 'public-read'
    };

    const result = await s3.upload(params).promise();
    
    console.log('Imagen subida exitosamente:', result.Location);
    
    res.json({
      success: true,
      imageUrl: result.Location,
      fileName: fileName,
      message: 'Imagen subida exitosamente a S3'
    });

  } catch (error) {
    console.error('Error al subir imagen:', error);
    res.status(500).json({
      error: 'Error al subir la imagen',
      details: error.message
    });
  }
});

// Ruta para procesar URL de imagen (simula procesamiento)
app.post('/process-image', async (req, res) => {
  try {
    const { imageUrl } = req.body;

    if (!imageUrl) {
      return res.status(400).json({ error: 'URL de imagen requerida' });
    }

    // Simular procesamiento de la imagen
    // En un caso real, aquí podrías hacer análisis de imagen, redimensionamiento, etc.
    const processedUrl = imageUrl;
    
    // Simular delay de procesamiento
    await new Promise(resolve => setTimeout(resolve, 1000));

    res.json({
      success: true,
      originalUrl: imageUrl,
      processedUrl: processedUrl,
      message: 'Imagen procesada exitosamente',
      processingTime: '1 segundo'
    });

  } catch (error) {
    console.error('Error al procesar imagen:', error);
    res.status(500).json({
      error: 'Error al procesar la imagen',
      details: error.message
    });
  }
});

// Ruta para obtener lista de imágenes en S3
app.get('/images', async (req, res) => {
  try {
    const params = {
      Bucket: process.env.S3_BUCKET_NAME,
      Prefix: 'images/'
    };

    const result = await s3.listObjectsV2(params).promise();
    
    const images = result.Contents.map(obj => ({
      key: obj.Key,
      url: `https://${process.env.S3_BUCKET_NAME}.s3.${process.env.AWS_REGION}.amazonaws.com/${obj.Key}`,
      size: obj.Size,
      lastModified: obj.LastModified
    }));

    res.json({
      success: true,
      images: images,
      count: images.length
    });

  } catch (error) {
    console.error('Error al listar imágenes:', error);
    res.status(500).json({
      error: 'Error al obtener lista de imágenes',
      details: error.message
    });
  }
});

// Manejo de errores
app.use((error, req, res, next) => {
  console.error('Error no manejado:', error);
  res.status(500).json({
    error: 'Error interno del servidor',
    details: error.message
  });
});

app.listen(PORT, () => {
  console.log(`🚀 Servidor corriendo en http://localhost:${PORT}`);
  console.log(`📱 Backend listo para conectar con Flutter`);
  console.log(`☁️  Configurado para AWS S3`);
}); 