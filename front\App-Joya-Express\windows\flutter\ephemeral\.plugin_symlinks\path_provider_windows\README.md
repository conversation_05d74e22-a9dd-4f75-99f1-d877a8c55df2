# path\_provider\_windows

The Windows implementation of [`path_provider`][1].

## Usage

This package is [endorsed][2], which means you can simply use `path_provider`
normally. This package will be automatically included in your app when you do,
so you do not need to add it to your `pubspec.yaml`.

However, if you `import` this package to use any of its APIs directly, you
should add it to your `pubspec.yaml` as usual.

[1]: https://pub.dev/packages/path_provider
[2]: https://flutter.dev/to/endorsed-federated-plugin
