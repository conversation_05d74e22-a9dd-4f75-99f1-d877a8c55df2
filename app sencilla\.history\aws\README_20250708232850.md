# 🚀 Configuración AWS con CloudFormation

Este directorio contiene todos los archivos necesarios para configurar automáticamente los recursos de AWS necesarios para la aplicación.

## 📁 Archivos Incluidos

- `s3-stack.yaml` - Template de CloudFormation
- `deploy-stack.sh` - <PERSON><PERSON><PERSON> de despliegue (Linux/Mac)
- `deploy-stack.bat` - <PERSON>ript de despliegue (Windows)
- `README.md` - Esta documentación

## 🎯 Recursos que se Crean

### 1. **Bucket S3**
- Almacenamiento para imágenes
- Configurado para acceso público de lectura
- CORS habilitado para la aplicación
- Versionado habilitado
- Lifecycle policies para limpieza automática

### 2. **Usuario IAM**
- Usuario dedicado para la aplicación
- Permisos específicos para S3
- Access Key y Secret Key generados automáticamente

### 3. **CloudWatch Logs**
- Grupo de logs para monitoreo
- Retención de 30 días

## 🚀 Despliegue Rápido

### Opción 1: Script Automático (Recomendado)

#### Linux/Mac:
```bash
cd aws
chmod +x deploy-stack.sh
./deploy-stack.sh
```

#### Windows:
```cmd
cd aws
deploy-stack.bat
```

### Opción 2: Comando Manual

```bash
# Configurar parámetros
BUCKET_NAME="mi-bucket-unico-123"
ENVIRONMENT="development"
REGION="us-east-1"

# Desplegar stack
aws cloudformation deploy \
    --template-file s3-stack.yaml \
    --stack-name app-sencilla-stack \
    --parameter-overrides \
        BucketName="$BUCKET_NAME" \
        Environment="$ENVIRONMENT" \
    --capabilities CAPABILITY_NAMED_IAM \
    --region "$REGION"
```

## 📋 Prerrequisitos

### 1. **AWS CLI Instalado**
```bash
# Verificar instalación
aws --version

# Si no está instalado:
# https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html
```

### 2. **Configuración AWS CLI**
```bash
# Configurar credenciales
aws configure

# Ingresar:
# - AWS Access Key ID
# - AWS Secret Access Key
# - Default region (ej: us-east-1)
# - Default output format (json)
```

### 3. **Permisos Requeridos**
Tu usuario AWS necesita permisos para:
- CloudFormation (crear/actualizar stacks)
- IAM (crear usuarios y políticas)
- S3 (crear buckets y políticas)
- CloudWatch Logs (crear log groups)

## 🔧 Parámetros del Stack

| Parámetro | Descripción | Valor por Defecto |
|-----------|-------------|-------------------|
| `BucketName` | Nombre único del bucket S3 | `app-sencilla-images-bucket` |
| `Environment` | Ambiente (dev/staging/prod) | `development` |

## 📊 Outputs del Stack

Después del despliegue, obtendrás:

- **BucketName**: Nombre del bucket creado
- **BucketURL**: URL pública del bucket
- **AccessKeyId**: Access Key para la aplicación
- **SecretAccessKey**: Secret Key para la aplicación
- **UserArn**: ARN del usuario IAM
- **LogGroupName**: Nombre del grupo de logs

## 🔄 Actualización del Stack

Para actualizar la configuración:

```bash
# Modificar el template s3-stack.yaml
# Luego ejecutar el script de despliegue nuevamente
./deploy-stack.sh
```

## 🗑️ Eliminación del Stack

```bash
# Eliminar stack completo
aws cloudformation delete-stack \
    --stack-name app-sencilla-stack \
    --region us-east-1

# Verificar eliminación
aws cloudformation describe-stacks \
    --stack-name app-sencilla-stack \
    --region us-east-1
```

## 🔍 Monitoreo

### Ver Estado del Stack
```bash
aws cloudformation describe-stacks \
    --stack-name app-sencilla-stack \
    --region us-east-1
```

### Ver Eventos del Stack
```bash
aws cloudformation describe-stack-events \
    --stack-name app-sencilla-stack \
    --region us-east-1
```

### Ver Recursos Creados
```bash
aws cloudformation list-stack-resources \
    --stack-name app-sencilla-stack \
    --region us-east-1
```

## 🛡️ Seguridad

### Configuración de Seguridad Incluida
- ✅ Bucket configurado para acceso público de lectura
- ✅ Usuario IAM con permisos mínimos necesarios
- ✅ Políticas de bucket restrictivas
- ✅ Logs de actividad habilitados

### Recomendaciones Adicionales
- 🔒 Rotar las credenciales regularmente
- 🔒 Usar AWS Secrets Manager para producción
- 🔒 Configurar alertas de CloudWatch
- 🔒 Revisar logs de acceso regularmente

## 🚨 Troubleshooting

### Error: "Bucket name already exists"
- Cambiar el nombre del bucket (debe ser único globalmente)
- Usar un sufijo con timestamp

### Error: "Insufficient permissions"
- Verificar que tu usuario tenga permisos de CloudFormation
- Verificar permisos de IAM y S3

### Error: "Template validation failed"
- Verificar sintaxis del YAML
- Usar `aws cloudformation validate-template --template-body file://s3-stack.yaml`

### Error: "Stack creation failed"
- Revisar eventos del stack en la consola de AWS
- Verificar logs de CloudFormation

## 📚 Recursos Adicionales

- [Documentación CloudFormation](https://docs.aws.amazon.com/cloudformation/)
- [Templates de ejemplo](https://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/cfn-sample-templates.html)
- [Mejores prácticas](https://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/best-practices.html)
- [Referencia de recursos](https://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-template-resource-type-ref.html)

## 🤝 Contribuir

1. Fork el proyecto
2. Crear rama para nueva funcionalidad
3. Modificar el template según necesidades
4. Probar el despliegue
5. Crear Pull Request

---

**¡Con este setup tendrás toda la infraestructura AWS configurada en minutos!** 🎉 