import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../../data/services/direct_upload_service.dart';
import '../../core/di/service_locator.dart';
import 'safe_network_image.dart';

class ImageUploadWidget extends StatefulWidget {
  final String? currentImageUrl;
  final String folder; // 'profile', 'vehicle', 'document'
  final Function(String imageUrl) onImageUploaded;
  final Function(String error)? onError;
  final double size;
  final bool isCircular;
  final String? placeholder;
  final bool showUploadButton;

  const ImageUploadWidget({
    Key? key,
    this.currentImageUrl,
    required this.folder,
    required this.onImageUploaded,
    this.onError,
    this.size = 120,
    this.isCircular = true,
    this.placeholder,
    this.showUploadButton = true,
  }) : super(key: key);

  @override
  State<ImageUploadWidget> createState() => _ImageUploadWidgetState();
}

class _ImageUploadWidgetState extends State<ImageUploadWidget> {
  final ImagePicker _picker = ImagePicker();
  final DirectUploadService _uploadService = sl<DirectUploadService>();

  bool _isUploading = false;
  String? _currentImageUrl;

  @override
  void initState() {
    super.initState();
    _currentImageUrl = widget.currentImageUrl;
  }

  @override
  void didUpdateWidget(ImageUploadWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentImageUrl != widget.currentImageUrl) {
      setState(() {
        _currentImageUrl = widget.currentImageUrl;
      });
    }
  }

  Future<void> _pickAndUploadImage(ImageSource source) async {
    try {
      final XFile? pickedFile = await _picker.pickImage(
        source: source,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (pickedFile == null) return;

      setState(() {
        _isUploading = true;
      });

      String imageUrl;

      if (kIsWeb) {
        // Para web, usar bytes
        final bytes = await pickedFile.readAsBytes();
        imageUrl = await _uploadService.uploadImageFromBytes(
          bytes: bytes,
          fileName: pickedFile.name,
          folder: widget.folder,
        );
      } else {
        // Para móvil, usar archivo
        final file = File(pickedFile.path);
        imageUrl = await _uploadService.uploadImage(
          file: file,
          folder: widget.folder,
        );
      }

      setState(() {
        _currentImageUrl = imageUrl;
        _isUploading = false;
      });

      widget.onImageUploaded(imageUrl);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Imagen subida correctamente'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isUploading = false;
      });

      final errorMessage = _getErrorMessage(e);
      widget.onError?.call(errorMessage);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(errorMessage), backgroundColor: Colors.red),
        );
      }
    }
  }

  String _getErrorMessage(dynamic error) {
    final errorStr = error.toString();
    if (errorStr.contains('Tipo de archivo no permitido')) {
      return 'Solo se permiten imágenes JPEG, PNG y WebP';
    } else if (errorStr.contains('Servicio de almacenamiento no disponible')) {
      return 'Servicio temporalmente no disponible';
    } else if (errorStr.contains('NetworkException')) {
      return 'Error de conexión. Verifica tu internet';
    } else {
      return 'Error subiendo imagen. Inténtalo de nuevo';
    }
  }

  void _showImageSourceDialog() {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Wrap(
            children: [
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('Galería'),
                onTap: () {
                  Navigator.of(context).pop();
                  _pickAndUploadImage(ImageSource.gallery);
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_camera),
                title: const Text('Cámara'),
                onTap: () {
                  Navigator.of(context).pop();
                  _pickAndUploadImage(ImageSource.camera);
                },
              ),
              if (_currentImageUrl != null)
                ListTile(
                  leading: const Icon(Icons.delete, color: Colors.red),
                  title: const Text('Eliminar imagen'),
                  onTap: () {
                    Navigator.of(context).pop();
                    _removeImage();
                  },
                ),
            ],
          ),
        );
      },
    );
  }

  void _removeImage() {
    setState(() {
      _currentImageUrl = null;
    });
    widget.onImageUploaded(''); // Enviar string vacío para indicar eliminación
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        GestureDetector(
          onTap: widget.showUploadButton ? _showImageSourceDialog : null,
          child: Container(
            width: widget.size,
            height: widget.size,
            decoration: BoxDecoration(
              shape: widget.isCircular ? BoxShape.circle : BoxShape.rectangle,
              borderRadius:
                  widget.isCircular ? null : BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade300, width: 2),
              color: Colors.grey.shade100,
            ),
            child:
                _isUploading
                    ? const Center(child: CircularProgressIndicator())
                    : _currentImageUrl != null && _currentImageUrl!.isNotEmpty
                    ? SafeNetworkImage(
                      imageUrl: _currentImageUrl,
                      width: widget.size,
                      height: widget.size,
                      fit: BoxFit.cover,
                      borderRadius:
                          widget.isCircular
                              ? BorderRadius.circular(widget.size / 2)
                              : BorderRadius.circular(10),
                      placeholder: Center(
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Theme.of(context).primaryColor,
                          ),
                        ),
                      ),
                      errorWidget: _buildPlaceholder(),
                    )
                    : _buildPlaceholder(),
          ),
        ),
        if (widget.showUploadButton) ...[
          const SizedBox(height: 8),
          TextButton.icon(
            onPressed: _isUploading ? null : _showImageSourceDialog,
            icon: const Icon(Icons.camera_alt),
            label: Text(
              _currentImageUrl != null ? 'Cambiar imagen' : 'Subir imagen',
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildPlaceholder() {
    return Container(
      width: widget.size,
      height: widget.size,
      decoration: BoxDecoration(
        shape: widget.isCircular ? BoxShape.circle : BoxShape.rectangle,
        borderRadius: widget.isCircular ? null : BorderRadius.circular(10),
        color: Colors.grey.shade200,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.add_a_photo,
            size: widget.size * 0.3,
            color: Colors.grey.shade400,
          ),
          if (widget.placeholder != null) ...[
            const SizedBox(height: 4),
            Text(
              widget.placeholder!,
              style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}
