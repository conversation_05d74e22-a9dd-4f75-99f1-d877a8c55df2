import 'package:flutter/material.dart';
import 'package:joya_express/core/constants/app_colors.dart';
import 'package:joya_express/core/constants/app_strings.dart';
import 'package:joya_express/core/constants/app_text_styles.dart';
import 'package:joya_express/presentation/modules/auth/Passenger/viewmodels/auth_viewmodel.dart';
import 'package:joya_express/presentation/widgets/simple_image_upload_widget.dart';
import 'package:provider/provider.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.surface,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back_ios,
            color: AppColors.primary,
            size: 20,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          AppStrings.profileTitle,
          style: AppTextStyles.poppinsHeading3,
        ),
        centerTitle: true,
      ),
      body: Consumer<AuthViewModel>(
        builder: (context, authViewModel, child) {
          final user = authViewModel.currentUser;

          if (user == null) {
            return const Center(
              child: CircularProgressIndicator(color: AppColors.primary),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                const SizedBox(height: 20),

                // Foto de perfil con S3
                _buildS3ProfilePhoto(user.profilePhoto),

                const SizedBox(height: 40),

                // Campo de nombre (no editable)
                _buildReadOnlyField(
                  label: AppStrings.profileName,
                  value: user.fullName,
                ),

                const SizedBox(height: 16),

                // Campo de email (no editable)
                _buildReadOnlyField(
                  label: AppStrings.profileEmail,
                  value: user.email ?? '',
                ),

                const SizedBox(height: 16),

                // Campo de teléfono (no editable)
                _buildPhoneField(user.phone),

                const SizedBox(height: 40),

                // Botón de guardar (solo para la foto)
                _buildSaveButton(),

                const SizedBox(height: 20),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildS3ProfilePhoto(String? photoUrl) {
    return Column(
      children: [
        SimpleImageUploadWidget(
          currentImageUrl: photoUrl,
          imageType: 'profile',
          onImageUploaded: (imageUrl) {
            print('✅ [PROFILE] Foto de perfil actualizada: $imageUrl');
            // Aquí podrías actualizar el estado del usuario si es necesario
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Foto de perfil actualizada correctamente'),
                  backgroundColor: AppColors.success,
                ),
              );
            }
          },
          onError: (error) {
            print('❌ [PROFILE] Error en foto de perfil: $error');
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Error al actualizar la foto: $error'),
                  backgroundColor: AppColors.error,
                ),
              );
            }
          },
          size: 120,
          isCircular: true,
          placeholder: 'Toca para cambiar foto',
        ),
        const SizedBox(height: 12),
        Text(
          AppStrings.profileUpdatePhoto,
          style: AppTextStyles.interBodySmall.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildReadOnlyField({required String label, required String value}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTextStyles.interBodySmall.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.greyLight,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.border),
          ),
          child: Text(
            value.isEmpty ? 'No especificado' : value,
            style: AppTextStyles.interInput.copyWith(
              color:
                  value.isEmpty
                      ? AppColors.textDisabled
                      : AppColors.textPrimary,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPhoneField(String phone) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppStrings.profilePhone,
          style: AppTextStyles.interBodySmall.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.greyLight,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.border),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppColors.textPrimary,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Icon(
                  Icons.phone,
                  color: AppColors.white,
                  size: 16,
                ),
              ),
              const SizedBox(width: 12),
              Text(phone, style: AppTextStyles.interInput),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _saveChanges,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Text(AppStrings.profileSave, style: AppTextStyles.poppinsButton),
      ),
    );
  }

  void _saveChanges() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Cambios guardados correctamente'),
        backgroundColor: AppColors.success,
      ),
    );
  }
}
