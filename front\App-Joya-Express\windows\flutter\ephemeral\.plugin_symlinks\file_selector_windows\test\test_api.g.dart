// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.
// Autogenerated from Pig<PERSON> (v22.4.1), do not edit directly.
// See also: https://pub.dev/packages/pigeon
// ignore_for_file: public_member_api_docs, non_constant_identifier_names, avoid_as, unused_import, unnecessary_parenthesis, unnecessary_import, no_leading_underscores_for_local_identifiers
// ignore_for_file: avoid_relative_lib_imports
import 'dart:async';
import 'dart:typed_data' show <PERSON>loat<PERSON><PERSON><PERSON>, Int32List, Int64List, Uint8List;
import 'package:flutter/foundation.dart' show ReadBuffer, WriteBuffer;
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:file_selector_windows/src/messages.g.dart';

class _PigeonCodec extends StandardMessageCodec {
  const _PigeonCodec();
  @override
  void writeValue(WriteBuffer buffer, Object? value) {
    if (value is int) {
      buffer.putUint8(4);
      buffer.putInt64(value);
    } else if (value is TypeGroup) {
      buffer.putUint8(129);
      writeValue(buffer, value.encode());
    } else if (value is SelectionOptions) {
      buffer.putUint8(130);
      writeValue(buffer, value.encode());
    } else if (value is FileDialogResult) {
      buffer.putUint8(131);
      writeValue(buffer, value.encode());
    } else {
      super.writeValue(buffer, value);
    }
  }

  @override
  Object? readValueOfType(int type, ReadBuffer buffer) {
    switch (type) {
      case 129:
        return TypeGroup.decode(readValue(buffer)!);
      case 130:
        return SelectionOptions.decode(readValue(buffer)!);
      case 131:
        return FileDialogResult.decode(readValue(buffer)!);
      default:
        return super.readValueOfType(type, buffer);
    }
  }
}

abstract class TestFileSelectorApi {
  static TestDefaultBinaryMessengerBinding? get _testBinaryMessengerBinding =>
      TestDefaultBinaryMessengerBinding.instance;
  static const MessageCodec<Object?> pigeonChannelCodec = _PigeonCodec();

  FileDialogResult showOpenDialog(SelectionOptions options,
      String? initialDirectory, String? confirmButtonText);

  FileDialogResult showSaveDialog(
      SelectionOptions options,
      String? initialDirectory,
      String? suggestedName,
      String? confirmButtonText);

  static void setUp(
    TestFileSelectorApi? api, {
    BinaryMessenger? binaryMessenger,
    String messageChannelSuffix = '',
  }) {
    messageChannelSuffix =
        messageChannelSuffix.isNotEmpty ? '.$messageChannelSuffix' : '';
    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.file_selector_windows.FileSelectorApi.showOpenDialog$messageChannelSuffix',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (api == null) {
        _testBinaryMessengerBinding!.defaultBinaryMessenger
            .setMockDecodedMessageHandler<Object?>(pigeonVar_channel, null);
      } else {
        _testBinaryMessengerBinding!.defaultBinaryMessenger
            .setMockDecodedMessageHandler<Object?>(pigeonVar_channel,
                (Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.file_selector_windows.FileSelectorApi.showOpenDialog was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final SelectionOptions? arg_options = (args[0] as SelectionOptions?);
          assert(arg_options != null,
              'Argument for dev.flutter.pigeon.file_selector_windows.FileSelectorApi.showOpenDialog was null, expected non-null SelectionOptions.');
          final String? arg_initialDirectory = (args[1] as String?);
          final String? arg_confirmButtonText = (args[2] as String?);
          try {
            final FileDialogResult output = api.showOpenDialog(
                arg_options!, arg_initialDirectory, arg_confirmButtonText);
            return <Object?>[output];
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }
    {
      final BasicMessageChannel<
          Object?> pigeonVar_channel = BasicMessageChannel<
              Object?>(
          'dev.flutter.pigeon.file_selector_windows.FileSelectorApi.showSaveDialog$messageChannelSuffix',
          pigeonChannelCodec,
          binaryMessenger: binaryMessenger);
      if (api == null) {
        _testBinaryMessengerBinding!.defaultBinaryMessenger
            .setMockDecodedMessageHandler<Object?>(pigeonVar_channel, null);
      } else {
        _testBinaryMessengerBinding!.defaultBinaryMessenger
            .setMockDecodedMessageHandler<Object?>(pigeonVar_channel,
                (Object? message) async {
          assert(message != null,
              'Argument for dev.flutter.pigeon.file_selector_windows.FileSelectorApi.showSaveDialog was null.');
          final List<Object?> args = (message as List<Object?>?)!;
          final SelectionOptions? arg_options = (args[0] as SelectionOptions?);
          assert(arg_options != null,
              'Argument for dev.flutter.pigeon.file_selector_windows.FileSelectorApi.showSaveDialog was null, expected non-null SelectionOptions.');
          final String? arg_initialDirectory = (args[1] as String?);
          final String? arg_suggestedName = (args[2] as String?);
          final String? arg_confirmButtonText = (args[3] as String?);
          try {
            final FileDialogResult output = api.showSaveDialog(arg_options!,
                arg_initialDirectory, arg_suggestedName, arg_confirmButtonText);
            return <Object?>[output];
          } on PlatformException catch (e) {
            return wrapResponse(error: e);
          } catch (e) {
            return wrapResponse(
                error: PlatformException(code: 'error', message: e.toString()));
          }
        });
      }
    }
  }
}
