const AWS = require('aws-sdk');
const multer = require('multer');
const { v4: uuidv4 } = require('uuid');

// Configurar AWS S3
const s3 = new AWS.S3({
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    region: process.env.S3_REGION || 'us-east-1',
    signatureVersion: 'v4'
});

// Configurar multer para manejar archivos en memoria
const upload = multer({
    storage: multer.memoryStorage(),
    limits: {
        fileSize: 10 * 1024 * 1024, // 10MB máximo
    },
    fileFilter: (req, file, cb) => {
        // Solo imágenes
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
        if (allowedTypes.includes(file.mimetype)) {
            cb(null, true);
        } else {
            cb(new Error('Solo se permiten imágenes JPEG, PNG y WebP'), false);
        }
    }
});

class ImageUploadController {
    /**
     * Middleware de multer
     */
    static getUploadMiddleware() {
        return upload.single('image');
    }

    /**
     * Subir imagen a S3 - Endpoint simple
     * POST /api/uploads/image
     */
    async uploadImage(req, res) {
        try {
            console.log('📤 [IMAGE-UPLOAD] Iniciando subida de imagen...');
            
            if (!req.file) {
                return res.status(400).json({
                    success: false,
                    message: 'No se proporcionó ninguna imagen'
                });
            }

            // Validar que S3 esté configurado
            if (!process.env.S3_BUCKET_NAME) {
                return res.status(503).json({
                    success: false,
                    message: 'Servicio de almacenamiento no configurado'
                });
            }

            const file = req.file;
            const userId = req.user?.id || 'anonymous';
            const userType = req.user?.tipo || 'usuario';
            const imageType = req.body.type || 'profile'; // 'profile' o 'vehicle'

            console.log('📁 [IMAGE-UPLOAD] Archivo:', file.originalname);
            console.log('👤 [IMAGE-UPLOAD] Usuario:', userId, '- Tipo:', userType);
            console.log('🖼️ [IMAGE-UPLOAD] Tipo de imagen:', imageType);

            // Determinar carpeta en S3 según el tipo de usuario y tipo de imagen
            let s3Folder;
            if (userType === 'conductor') {
                s3Folder = imageType === 'vehicle' ? 'vehiculos' : 'conductores/perfiles';
            } else {
                s3Folder = 'usuarios/perfiles';
            }
            
            // Generar nombre único para el archivo
            const fileExtension = this.getFileExtension(file.mimetype);
            const fileName = `${uuidv4()}${fileExtension}`;
            const key = `${s3Folder}/${userId}/${fileName}`;

            console.log('🔑 [IMAGE-UPLOAD] Key S3:', key);

            // Parámetros para S3
            const params = {
                Bucket: process.env.S3_BUCKET_NAME,
                Key: key,
                Body: file.buffer,
                ContentType: file.mimetype,
                ACL: 'public-read'
            };

            // Subir a S3
            console.log('☁️ [IMAGE-UPLOAD] Subiendo a S3...');
            const result = await s3.upload(params).promise();
            
            console.log('✅ [IMAGE-UPLOAD] Imagen subida exitosamente:', result.Location);
            
            // Respuesta simple como en tu app sencilla
            res.json({
                success: true,
                url: result.Location,
                message: 'Imagen subida exitosamente'
            });

        } catch (error) {
            console.error('❌ [IMAGE-UPLOAD] Error al subir imagen:', error);
            
            if (error.message.includes('Solo se permiten imágenes')) {
                return res.status(400).json({
                    success: false,
                    message: error.message
                });
            }

            res.status(500).json({
                success: false,
                message: 'Error al subir la imagen',
                error: error.message
            });
        }
    }

    /**
     * Obtener extensión de archivo basada en el tipo MIME
     */
    getFileExtension(mimeType) {
        const extensions = {
            'image/jpeg': '.jpg',
            'image/jpg': '.jpg',
            'image/png': '.png',
            'image/webp': '.webp'
        };
        return extensions[mimeType] || '.jpg';
    }
}

module.exports = new ImageUploadController();
