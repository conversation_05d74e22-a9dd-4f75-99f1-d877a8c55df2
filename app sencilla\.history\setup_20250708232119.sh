#!/bin/bash

echo "🚀 Configurando App Sencilla - Flutter + AWS S3 + Backend"
echo "=================================================="

# Verificar si Node.js está instalado
if ! command -v node &> /dev/null; then
    echo "❌ Node.js no está instalado. Por favor instala Node.js primero."
    exit 1
fi

# Verificar si Flutter está instalado
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter no está instalado. Por favor instala Flutter primero."
    exit 1
fi

echo "✅ Node.js y Flutter detectados"

# Instalar dependencias del backend
echo "📦 Instalando dependencias del backend..."
cd backend
npm install
cd ..

# Instalar dependencias de Flutter
echo "📱 Instalando dependencias de Flutter..."
cd frontend
flutter pub get
cd ..

echo ""
echo "✅ Instalación completada!"
echo ""
echo "📋 Próximos pasos:"
echo "1. Configura tus credenciales de AWS en backend/.env"
echo "2. Inicia el backend: cd backend && npm start"
echo "3. Ejecuta la app Flutter: cd frontend && flutter run"
echo ""
echo "🔧 Configuración AWS S3 requerida:"
echo "- AWS_ACCESS_KEY_ID"
echo "- AWS_SECRET_ACCESS_KEY" 
echo "- AWS_REGION"
echo "- S3_BUCKET_NAME"
echo ""
echo "🌐 URLs del backend:"
echo "- http://localhost:3000 (desarrollo local)"
echo "- http://********:3000 (emulador Android)"
echo "- http://localhost:3000 (iOS simulator)" 