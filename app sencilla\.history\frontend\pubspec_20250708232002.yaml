name: app_sencilla
description: App Flutter para subir imágenes a S3 y procesarlas

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  
  # UI
  cupertino_icons: ^1.0.2
  
  # HTTP requests
  http: ^1.1.0
  
  # Image picker
  image_picker: ^1.0.4
  
  # Image display
  cached_network_image: ^3.3.0
  
  # State management
  provider: ^6.1.1
  
  # File handling
  path: ^1.8.3
  
  # Permissions
  permission_handler: ^11.0.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
  
  fonts:
    - family: Roboto
      fonts:
        - asset: fonts/Roboto-Regular.ttf
        - asset: fonts/Roboto-Bold.ttf
          weight: 700 