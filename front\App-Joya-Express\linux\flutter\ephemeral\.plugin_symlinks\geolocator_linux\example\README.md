# geolocator_linux_example

Demonstrates how to use the geolocator_linux plugin.

## Getting Started

To run the example App take the following steps:

1. Change into the `geolocator_linux/example` directory (assuming the current directory is the root of the repository execute `cd geolocator_linux/example`);
2. Run `flutter pub get` to download all required dependencies;
3. Finally run `flutter run` to run the example App.
