name: shared_preferences_windows_example
description: Demonstrates how to use the shared_preferences_windows plugin.
publish_to: none

environment:
  sdk: ^3.3.0
  flutter: ">=3.19.0"

dependencies:
  flutter:
    sdk: flutter
  shared_preferences_platform_interface: ^2.4.0
  shared_preferences_windows:
    # When depending on this package from a real application you should use:
    #   shared_preferences_windows: ^x.y.z
    # See https://dart.dev/tools/pub/dependencies#version-constraints
    # The example app is bundled with the plugin so we use a path dependency on
    # the parent directory to use the current plugin's version.
    path: ../

dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter

flutter:
  uses-material-design: true
