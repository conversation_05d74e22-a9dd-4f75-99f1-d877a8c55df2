name: package_info_plus
description: Flutter plugin for querying information about the application package, such as CFBundleVersion on iOS or versionCode on Android.
version: 8.3.0
homepage: https://github.com/fluttercommunity/plus_plugins
repository: https://github.com/fluttercommunity/plus_plugins/tree/main/packages/package_info_plus/package_info_plus
issue_tracker: https://github.com/fluttercommunity/plus_plugins/labels/package_info_plus
topics:
  - information
  - identifier
  - utils

flutter:
  plugin:
    platforms:
      android:
        package: dev.fluttercommunity.plus.packageinfo
        pluginClass: PackageInfoPlugin
      ios:
        pluginClass: FPPPackageInfoPlusPlugin
      linux:
        dartPluginClass: PackageInfoPlusLinuxPlugin
      macos:
        pluginClass: FPPPackageInfoPlusPlugin
      web:
        pluginClass: PackageInfoPlusWebPlugin
        fileName: src/package_info_plus_web.dart
      windows:
        dartPluginClass: PackageInfoPlusWindowsPlugin

dependencies:
  ffi: ^2.0.1
  flutter:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter
  http: ">=0.13.5 <2.0.0"
  meta: ^1.8.0
  path: ^1.8.2
  package_info_plus_platform_interface: ^3.2.0
  web: ">=0.5.0 <2.0.0"
  win32: ^5.5.3
  clock: ^1.1.1

dev_dependencies:
  flutter_lints: ">=4.0.0 <6.0.0"
  flutter_test:
    sdk: flutter
  test: ^1.22.0

environment:
  sdk: ">=3.3.0 <4.0.0"
  flutter: ">=3.19.0"
