AWSTemplateFormatVersion: '2010-09-09'
Description: 'App Sencilla - S3 Bucket y recursos IAM para subida de imágenes'

Parameters:
  BucketName:
    Type: String
    Description: Nombre del bucket S3 (debe ser único globalmente)
    Default: app-sencilla-images-bucket
    AllowedPattern: '^[a-z0-9-]+$'
    ConstraintDescription: Solo letras minúsculas, números y guiones
  
  Environment:
    Type: String
    Description: Ambiente de la aplicación
    Default: development
    AllowedValues:
      - development
      - staging
      - production
    ConstraintDescription: Debe ser development, staging o production

Resources:
  # Bucket S3 principal
  ImageBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Ref BucketName
      PublicAccessBlockConfiguration:
        BlockPublicAcls: false
        BlockPublicPolicy: false
        IgnorePublicAcls: false
        RestrictPublicBuckets: false
      CorsConfiguration:
        CorsRules:
          - AllowedHeaders:
              - '*'
            AllowedMethods:
              - GET
              - PUT
              - POST
              - DELETE
              - HEAD
            AllowedOrigins:
              - '*'
            MaxAge: 3000
      VersioningConfiguration:
        Status: Enabled
      LifecycleConfiguration:
        Rules:
          - Id: DeleteOldVersions
            Status: Enabled
            NoncurrentVersionExpirationInDays: 30
          - Id: DeleteIncompleteMultipartUploads
            Status: Enabled
            AbortIncompleteMultipartUpload:
              DaysAfterInitiation: 7
      Tags:
        - Key: Environment
          Value: !Ref Environment
        - Key: Project
          Value: AppSencilla
        - Key: Purpose
          Value: ImageStorage

  # Política de bucket para acceso público de lectura
  BucketPolicy:
    Type: AWS::S3::BucketPolicy
    Properties:
      Bucket: !Ref ImageBucket
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Sid: PublicReadGetObject
            Effect: Allow
            Principal: '*'
            Action: 's3:GetObject'
            Resource: !Sub 'arn:aws:s3:::${ImageBucket}/*'
          - Sid: AllowAppUploads
            Effect: Allow
            Principal:
              AWS: !GetAtt AppUser.Arn
            Action:
              - 's3:PutObject'
              - 's3:PutObjectAcl'
              - 's3:DeleteObject'
            Resource: !Sub 'arn:aws:s3:::${ImageBucket}/*'

  # Usuario IAM para la aplicación
  AppUser:
    Type: AWS::IAM::User
    Properties:
      UserName: !Sub 'app-sencilla-user-${Environment}'
      Path: '/app-sencilla/'
      Tags:
        - Key: Environment
          Value: !Ref Environment
        - Key: Project
          Value: AppSencilla

  # Política IAM para el usuario
  AppUserPolicy:
    Type: AWS::IAM::Policy
    Properties:
      PolicyName: !Sub 'app-sencilla-s3-policy-${Environment}'
      Users:
        - !Ref AppUser
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Sid: S3BucketAccess
            Effect: Allow
            Action:
              - 's3:GetObject'
              - 's3:PutObject'
              - 's3:PutObjectAcl'
              - 's3:DeleteObject'
              - 's3:ListBucket'
            Resource:
              - !Sub 'arn:aws:s3:::${ImageBucket}'
              - !Sub 'arn:aws:s3:::${ImageBucket}/*'
          - Sid: CloudWatchLogs
            Effect: Allow
            Action:
              - 'logs:CreateLogGroup'
              - 'logs:CreateLogStream'
              - 'logs:PutLogEvents'
            Resource: '*'

  # Access Key para el usuario
  AppUserAccessKey:
    Type: AWS::IAM::AccessKey
    Properties:
      UserName: !Ref AppUser
      Status: Active

  # CloudWatch Log Group para logs de la aplicación
  AppLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub '/aws/app-sencilla/${Environment}'
      RetentionInDays: 30

Outputs:
  BucketName:
    Description: Nombre del bucket S3 creado
    Value: !Ref ImageBucket
    Export:
      Name: !Sub '${AWS::StackName}-BucketName'

  BucketURL:
    Description: URL del bucket S3
    Value: !Sub 'https://${ImageBucket}.s3.${AWS::Region}.amazonaws.com'
    Export:
      Name: !Sub '${AWS::StackName}-BucketURL'

  AccessKeyId:
    Description: Access Key ID para la aplicación
    Value: !Ref AppUserAccessKey
    Export:
      Name: !Sub '${AWS::StackName}-AccessKeyId'

  SecretAccessKey:
    Description: Secret Access Key para la aplicación (guardar de forma segura)
    Value: !GetAtt AppUserAccessKey.SecretAccessKey
    Export:
      Name: !Sub '${AWS::StackName}-SecretAccessKey'

  UserArn:
    Description: ARN del usuario IAM creado
    Value: !GetAtt AppUser.Arn
    Export:
      Name: !Sub '${AWS::StackName}-UserArn'

  LogGroupName:
    Description: Nombre del grupo de logs
    Value: !Ref AppLogGroup
    Export:
      Name: !Sub '${AWS::StackName}-LogGroupName' 