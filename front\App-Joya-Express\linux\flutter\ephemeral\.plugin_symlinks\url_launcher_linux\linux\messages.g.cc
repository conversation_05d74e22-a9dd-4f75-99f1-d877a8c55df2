// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.
// Autogenerated from Pigeon (v22.6.1), do not edit directly.
// See also: https://pub.dev/packages/pigeon

#include "messages.g.h"

struct _FulMessageCodec {
  FlStandardMessageCodec parent_instance;
};

G_DEFINE_TYPE(FulMessageCodec, ful_message_codec,
              fl_standard_message_codec_get_type())

static gboolean ful_message_codec_write_value(FlStandardMessageCodec* codec,
                                              GByteArray* buffer,
                                              FlValue* value, GError** error) {
  if (fl_value_get_type(value) == FL_VALUE_TYPE_CUSTOM) {
    switch (fl_value_get_custom_type(value)) {}
  }

  return FL_STANDARD_MESSAGE_CODEC_CLASS(ful_message_codec_parent_class)
      ->write_value(codec, buffer, value, error);
}

static FlValue* ful_message_codec_read_value_of_type(
    FlStandardMessageCodec* codec, GBytes* buffer, size_t* offset, int type,
    GError** error) {
  switch (type) {
    default:
      return FL_STANDARD_MESSAGE_CODEC_CLASS(ful_message_codec_parent_class)
          ->read_value_of_type(codec, buffer, offset, type, error);
  }
}

static void ful_message_codec_init(FulMessageCodec* self) {}

static void ful_message_codec_class_init(FulMessageCodecClass* klass) {
  FL_STANDARD_MESSAGE_CODEC_CLASS(klass)->write_value =
      ful_message_codec_write_value;
  FL_STANDARD_MESSAGE_CODEC_CLASS(klass)->read_value_of_type =
      ful_message_codec_read_value_of_type;
}

static FulMessageCodec* ful_message_codec_new() {
  FulMessageCodec* self =
      FUL_MESSAGE_CODEC(g_object_new(ful_message_codec_get_type(), nullptr));
  return self;
}

struct _FulUrlLauncherApiCanLaunchUrlResponse {
  GObject parent_instance;

  FlValue* value;
};

G_DEFINE_TYPE(FulUrlLauncherApiCanLaunchUrlResponse,
              ful_url_launcher_api_can_launch_url_response, G_TYPE_OBJECT)

static void ful_url_launcher_api_can_launch_url_response_dispose(
    GObject* object) {
  FulUrlLauncherApiCanLaunchUrlResponse* self =
      FUL_URL_LAUNCHER_API_CAN_LAUNCH_URL_RESPONSE(object);
  g_clear_pointer(&self->value, fl_value_unref);
  G_OBJECT_CLASS(ful_url_launcher_api_can_launch_url_response_parent_class)
      ->dispose(object);
}

static void ful_url_launcher_api_can_launch_url_response_init(
    FulUrlLauncherApiCanLaunchUrlResponse* self) {}

static void ful_url_launcher_api_can_launch_url_response_class_init(
    FulUrlLauncherApiCanLaunchUrlResponseClass* klass) {
  G_OBJECT_CLASS(klass)->dispose =
      ful_url_launcher_api_can_launch_url_response_dispose;
}

FulUrlLauncherApiCanLaunchUrlResponse*
ful_url_launcher_api_can_launch_url_response_new(gboolean return_value) {
  FulUrlLauncherApiCanLaunchUrlResponse* self =
      FUL_URL_LAUNCHER_API_CAN_LAUNCH_URL_RESPONSE(g_object_new(
          ful_url_launcher_api_can_launch_url_response_get_type(), nullptr));
  self->value = fl_value_new_list();
  fl_value_append_take(self->value, fl_value_new_bool(return_value));
  return self;
}

FulUrlLauncherApiCanLaunchUrlResponse*
ful_url_launcher_api_can_launch_url_response_new_error(const gchar* code,
                                                       const gchar* message,
                                                       FlValue* details) {
  FulUrlLauncherApiCanLaunchUrlResponse* self =
      FUL_URL_LAUNCHER_API_CAN_LAUNCH_URL_RESPONSE(g_object_new(
          ful_url_launcher_api_can_launch_url_response_get_type(), nullptr));
  self->value = fl_value_new_list();
  fl_value_append_take(self->value, fl_value_new_string(code));
  fl_value_append_take(self->value,
                       fl_value_new_string(message != nullptr ? message : ""));
  fl_value_append_take(self->value, details != nullptr ? fl_value_ref(details)
                                                       : fl_value_new_null());
  return self;
}

struct _FulUrlLauncherApiLaunchUrlResponse {
  GObject parent_instance;

  FlValue* value;
};

G_DEFINE_TYPE(FulUrlLauncherApiLaunchUrlResponse,
              ful_url_launcher_api_launch_url_response, G_TYPE_OBJECT)

static void ful_url_launcher_api_launch_url_response_dispose(GObject* object) {
  FulUrlLauncherApiLaunchUrlResponse* self =
      FUL_URL_LAUNCHER_API_LAUNCH_URL_RESPONSE(object);
  g_clear_pointer(&self->value, fl_value_unref);
  G_OBJECT_CLASS(ful_url_launcher_api_launch_url_response_parent_class)
      ->dispose(object);
}

static void ful_url_launcher_api_launch_url_response_init(
    FulUrlLauncherApiLaunchUrlResponse* self) {}

static void ful_url_launcher_api_launch_url_response_class_init(
    FulUrlLauncherApiLaunchUrlResponseClass* klass) {
  G_OBJECT_CLASS(klass)->dispose =
      ful_url_launcher_api_launch_url_response_dispose;
}

FulUrlLauncherApiLaunchUrlResponse*
ful_url_launcher_api_launch_url_response_new(const gchar* return_value) {
  FulUrlLauncherApiLaunchUrlResponse* self =
      FUL_URL_LAUNCHER_API_LAUNCH_URL_RESPONSE(g_object_new(
          ful_url_launcher_api_launch_url_response_get_type(), nullptr));
  self->value = fl_value_new_list();
  fl_value_append_take(self->value, return_value != nullptr
                                        ? fl_value_new_string(return_value)
                                        : fl_value_new_null());
  return self;
}

FulUrlLauncherApiLaunchUrlResponse*
ful_url_launcher_api_launch_url_response_new_error(const gchar* code,
                                                   const gchar* message,
                                                   FlValue* details) {
  FulUrlLauncherApiLaunchUrlResponse* self =
      FUL_URL_LAUNCHER_API_LAUNCH_URL_RESPONSE(g_object_new(
          ful_url_launcher_api_launch_url_response_get_type(), nullptr));
  self->value = fl_value_new_list();
  fl_value_append_take(self->value, fl_value_new_string(code));
  fl_value_append_take(self->value,
                       fl_value_new_string(message != nullptr ? message : ""));
  fl_value_append_take(self->value, details != nullptr ? fl_value_ref(details)
                                                       : fl_value_new_null());
  return self;
}

struct _FulUrlLauncherApi {
  GObject parent_instance;

  const FulUrlLauncherApiVTable* vtable;
  gpointer user_data;
  GDestroyNotify user_data_free_func;
};

G_DEFINE_TYPE(FulUrlLauncherApi, ful_url_launcher_api, G_TYPE_OBJECT)

static void ful_url_launcher_api_dispose(GObject* object) {
  FulUrlLauncherApi* self = FUL_URL_LAUNCHER_API(object);
  if (self->user_data != nullptr) {
    self->user_data_free_func(self->user_data);
  }
  self->user_data = nullptr;
  G_OBJECT_CLASS(ful_url_launcher_api_parent_class)->dispose(object);
}

static void ful_url_launcher_api_init(FulUrlLauncherApi* self) {}

static void ful_url_launcher_api_class_init(FulUrlLauncherApiClass* klass) {
  G_OBJECT_CLASS(klass)->dispose = ful_url_launcher_api_dispose;
}

static FulUrlLauncherApi* ful_url_launcher_api_new(
    const FulUrlLauncherApiVTable* vtable, gpointer user_data,
    GDestroyNotify user_data_free_func) {
  FulUrlLauncherApi* self = FUL_URL_LAUNCHER_API(
      g_object_new(ful_url_launcher_api_get_type(), nullptr));
  self->vtable = vtable;
  self->user_data = user_data;
  self->user_data_free_func = user_data_free_func;
  return self;
}

static void ful_url_launcher_api_can_launch_url_cb(
    FlBasicMessageChannel* channel, FlValue* message_,
    FlBasicMessageChannelResponseHandle* response_handle, gpointer user_data) {
  FulUrlLauncherApi* self = FUL_URL_LAUNCHER_API(user_data);

  if (self->vtable == nullptr || self->vtable->can_launch_url == nullptr) {
    return;
  }

  FlValue* value0 = fl_value_get_list_value(message_, 0);
  const gchar* url = fl_value_get_string(value0);
  g_autoptr(FulUrlLauncherApiCanLaunchUrlResponse) response =
      self->vtable->can_launch_url(url, self->user_data);
  if (response == nullptr) {
    g_warning("No response returned to %s.%s", "UrlLauncherApi",
              "canLaunchUrl");
    return;
  }

  g_autoptr(GError) error = NULL;
  if (!fl_basic_message_channel_respond(channel, response_handle,
                                        response->value, &error)) {
    g_warning("Failed to send response to %s.%s: %s", "UrlLauncherApi",
              "canLaunchUrl", error->message);
  }
}

static void ful_url_launcher_api_launch_url_cb(
    FlBasicMessageChannel* channel, FlValue* message_,
    FlBasicMessageChannelResponseHandle* response_handle, gpointer user_data) {
  FulUrlLauncherApi* self = FUL_URL_LAUNCHER_API(user_data);

  if (self->vtable == nullptr || self->vtable->launch_url == nullptr) {
    return;
  }

  FlValue* value0 = fl_value_get_list_value(message_, 0);
  const gchar* url = fl_value_get_string(value0);
  g_autoptr(FulUrlLauncherApiLaunchUrlResponse) response =
      self->vtable->launch_url(url, self->user_data);
  if (response == nullptr) {
    g_warning("No response returned to %s.%s", "UrlLauncherApi", "launchUrl");
    return;
  }

  g_autoptr(GError) error = NULL;
  if (!fl_basic_message_channel_respond(channel, response_handle,
                                        response->value, &error)) {
    g_warning("Failed to send response to %s.%s: %s", "UrlLauncherApi",
              "launchUrl", error->message);
  }
}

void ful_url_launcher_api_set_method_handlers(
    FlBinaryMessenger* messenger, const gchar* suffix,
    const FulUrlLauncherApiVTable* vtable, gpointer user_data,
    GDestroyNotify user_data_free_func) {
  g_autofree gchar* dot_suffix =
      suffix != nullptr ? g_strdup_printf(".%s", suffix) : g_strdup("");
  g_autoptr(FulUrlLauncherApi) api_data =
      ful_url_launcher_api_new(vtable, user_data, user_data_free_func);

  g_autoptr(FulMessageCodec) codec = ful_message_codec_new();
  g_autofree gchar* can_launch_url_channel_name = g_strdup_printf(
      "dev.flutter.pigeon.url_launcher_linux.UrlLauncherApi.canLaunchUrl%s",
      dot_suffix);
  g_autoptr(FlBasicMessageChannel) can_launch_url_channel =
      fl_basic_message_channel_new(messenger, can_launch_url_channel_name,
                                   FL_MESSAGE_CODEC(codec));
  fl_basic_message_channel_set_message_handler(
      can_launch_url_channel, ful_url_launcher_api_can_launch_url_cb,
      g_object_ref(api_data), g_object_unref);
  g_autofree gchar* launch_url_channel_name = g_strdup_printf(
      "dev.flutter.pigeon.url_launcher_linux.UrlLauncherApi.launchUrl%s",
      dot_suffix);
  g_autoptr(FlBasicMessageChannel) launch_url_channel =
      fl_basic_message_channel_new(messenger, launch_url_channel_name,
                                   FL_MESSAGE_CODEC(codec));
  fl_basic_message_channel_set_message_handler(
      launch_url_channel, ful_url_launcher_api_launch_url_cb,
      g_object_ref(api_data), g_object_unref);
}

void ful_url_launcher_api_clear_method_handlers(FlBinaryMessenger* messenger,
                                                const gchar* suffix) {
  g_autofree gchar* dot_suffix =
      suffix != nullptr ? g_strdup_printf(".%s", suffix) : g_strdup("");

  g_autoptr(FulMessageCodec) codec = ful_message_codec_new();
  g_autofree gchar* can_launch_url_channel_name = g_strdup_printf(
      "dev.flutter.pigeon.url_launcher_linux.UrlLauncherApi.canLaunchUrl%s",
      dot_suffix);
  g_autoptr(FlBasicMessageChannel) can_launch_url_channel =
      fl_basic_message_channel_new(messenger, can_launch_url_channel_name,
                                   FL_MESSAGE_CODEC(codec));
  fl_basic_message_channel_set_message_handler(can_launch_url_channel, nullptr,
                                               nullptr, nullptr);
  g_autofree gchar* launch_url_channel_name = g_strdup_printf(
      "dev.flutter.pigeon.url_launcher_linux.UrlLauncherApi.launchUrl%s",
      dot_suffix);
  g_autoptr(FlBasicMessageChannel) launch_url_channel =
      fl_basic_message_channel_new(messenger, launch_url_channel_name,
                                   FL_MESSAGE_CODEC(codec));
  fl_basic_message_channel_set_message_handler(launch_url_channel, nullptr,
                                               nullptr, nullptr);
}
