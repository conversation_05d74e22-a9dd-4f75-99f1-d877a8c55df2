import 'dart:io';
import 'package:flutter/foundation.dart';
import '../../data/services/upload_service.dart';
import '../../core/network/api_exceptions.dart';

class UploadProvider extends ChangeNotifier {
  final UploadService _uploadService;

  UploadProvider(this._uploadService);

  bool _isUploading = false;
  String? _error;
  double _uploadProgress = 0.0;
  StorageInfo? _storageInfo;

  // Getters
  bool get isUploading => _isUploading;
  String? get error => _error;
  double get uploadProgress => _uploadProgress;
  StorageInfo? get storageInfo => _storageInfo;

  /// Subir imagen desde archivo
  Future<String?> uploadImage({
    required File file,
    required String folder,
  }) async {
    try {
      _setUploading(true);
      _clearError();

      final imageUrl = await _uploadService.uploadImage(
        file: file,
        folder: folder,
      );

      _setUploading(false);
      return imageUrl;
    } catch (e) {
      _setError(_getErrorMessage(e));
      _setUploading(false);
      return null;
    }
  }

  /// Subir imagen desde bytes (para web)
  Future<String?> uploadImageFromBytes({
    required Uint8List bytes,
    required String fileName,
    required String folder,
  }) async {
    try {
      _setUploading(true);
      _clearError();

      final imageUrl = await _uploadService.uploadImageFromBytes(
        bytes: bytes,
        fileName: fileName,
        folder: folder,
      );

      _setUploading(false);
      return imageUrl;
    } catch (e) {
      _setError(_getErrorMessage(e));
      _setUploading(false);
      return null;
    }
  }

  /// Eliminar imagen
  Future<bool> deleteImage(String key) async {
    try {
      _clearError();
      return await _uploadService.deleteImage(key);
    } catch (e) {
      _setError(_getErrorMessage(e));
      return false;
    }
  }

  /// Obtener información del servicio de almacenamiento
  Future<void> loadStorageInfo() async {
    try {
      _clearError();
      _storageInfo = await _uploadService.getStorageInfo();
      notifyListeners();
    } catch (e) {
      _setError(_getErrorMessage(e));
    }
  }

  /// Limpiar error
  void clearError() {
    _clearError();
  }

  // Métodos privados
  void _setUploading(bool uploading) {
    _isUploading = uploading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  void _setProgress(double progress) {
    _uploadProgress = progress;
    notifyListeners();
  }

  String _getErrorMessage(dynamic error) {
    if (error is ValidationException) {
      return error.message;
    } else if (error is ServiceUnavailableException) {
      return 'Servicio de almacenamiento temporalmente no disponible';
    } else if (error is NetworkException) {
      return 'Error de conexión. Verifica tu internet';
    } else if (error is UnauthorizedException) {
      return 'No tienes permisos para realizar esta acción';
    } else if (error is NotFoundException) {
      return 'Archivo no encontrado';
    } else {
      return 'Error inesperado. Inténtalo de nuevo';
    }
  }
}

/// Estados de upload
enum UploadState { idle, uploading, success, error }

/// Modelo para el resultado de upload
class UploadResult {
  final bool success;
  final String? imageUrl;
  final String? error;

  UploadResult({required this.success, this.imageUrl, this.error});

  factory UploadResult.success(String imageUrl) {
    return UploadResult(success: true, imageUrl: imageUrl);
  }

  factory UploadResult.error(String error) {
    return UploadResult(success: false, error: error);
  }
}
