class ApiException implements Exception {
  final String message;
  final int? statusCode;
  final String? errorType;

  ApiException({required this.message, this.statusCode, this.errorType});

  @override
  String toString() => 'ApiException: $message (Status: $statusCode)';
}

class NetworkException extends ApiException {
  NetworkException({String? message})
    : super(message: message ?? 'Error de conexión a internet');
}

class ServerException extends ApiException {
  ServerException({String? message, super.statusCode})
    : super(message: message ?? 'Error del servidor');
}

class AuthException extends ApiException {
  AuthException({String? message})
    : super(message: message ?? 'Error de autenticación');
}

class ValidationException extends ApiException {
  ValidationException({String? message})
    : super(message: message ?? 'Datos inválidos');
}

class NotFoundException extends ApiException {
  NotFoundException({String? message})
    : super(message: message ?? 'Recurso no encontrado', statusCode: 404);
}

class UnauthorizedException extends ApiException {
  UnauthorizedException({String? message})
    : super(message: message ?? 'No autorizado', statusCode: 401);
}

class ServiceUnavailableException extends ApiException {
  ServiceUnavailableException({String? message})
    : super(message: message ?? 'Servicio no disponible', statusCode: 503);
}

class UnknownException extends ApiException {
  UnknownException({String? message})
    : super(message: message ?? 'Error desconocido');
}
