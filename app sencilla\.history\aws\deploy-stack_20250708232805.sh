#!/bin/bash

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Desplegando Stack CloudFormation para App Sencilla${NC}"
echo "=================================================="

# Verificar si AWS CLI está instalado
if ! command -v aws &> /dev/null; then
    echo -e "${RED}❌ AWS CLI no está instalado. Por favor instala AWS CLI primero.${NC}"
    echo "Instrucciones: https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html"
    exit 1
fi

# Verificar si el usuario está autenticado
if ! aws sts get-caller-identity &> /dev/null; then
    echo -e "${RED}❌ No estás autenticado en AWS CLI.${NC}"
    echo "Ejecuta: aws configure"
    exit 1
fi

# Configuración
STACK_NAME="app-sencilla-stack"
TEMPLATE_FILE="s3-stack.yaml"
REGION=$(aws configure get region || echo "us-east-1")

echo -e "${YELLOW}📋 Configuración:${NC}"
echo "Stack Name: $STACK_NAME"
echo "Template: $TEMPLATE_FILE"
echo "Region: $REGION"
echo ""

# Solicitar nombre del bucket
read -p "Nombre del bucket S3 (debe ser único globalmente): " BUCKET_NAME
if [ -z "$BUCKET_NAME" ]; then
    BUCKET_NAME="app-sencilla-images-bucket-$(date +%s)"
    echo -e "${YELLOW}Usando nombre por defecto: $BUCKET_NAME${NC}"
fi

# Solicitar ambiente
read -p "Ambiente (development/staging/production) [development]: " ENVIRONMENT
if [ -z "$ENVIRONMENT" ]; then
    ENVIRONMENT="development"
fi

echo ""
echo -e "${YELLOW}🔧 Desplegando stack...${NC}"

# Desplegar stack
aws cloudformation deploy \
    --template-file "$TEMPLATE_FILE" \
    --stack-name "$STACK_NAME" \
    --parameter-overrides \
        BucketName="$BUCKET_NAME" \
        Environment="$ENVIRONMENT" \
    --capabilities CAPABILITY_NAMED_IAM \
    --region "$REGION"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Stack desplegado exitosamente!${NC}"
    
    # Obtener outputs
    echo ""
    echo -e "${BLUE}📊 Información del stack:${NC}"
    
    # Obtener todos los outputs
    OUTPUTS=$(aws cloudformation describe-stacks \
        --stack-name "$STACK_NAME" \
        --region "$REGION" \
        --query 'Stacks[0].Outputs' \
        --output json)
    
    # Parsear outputs
    BUCKET_NAME_OUTPUT=$(echo "$OUTPUTS" | jq -r '.[] | select(.OutputKey=="BucketName") | .OutputValue')
    BUCKET_URL=$(echo "$OUTPUTS" | jq -r '.[] | select(.OutputKey=="BucketURL") | .OutputValue')
    ACCESS_KEY_ID=$(echo "$OUTPUTS" | jq -r '.[] | select(.OutputKey=="AccessKeyId") | .OutputValue')
    SECRET_ACCESS_KEY=$(echo "$OUTPUTS" | jq -r '.[] | select(.OutputKey=="SecretAccessKey") | .OutputValue')
    
    echo "Bucket Name: $BUCKET_NAME_OUTPUT"
    echo "Bucket URL: $BUCKET_URL"
    echo "Access Key ID: $ACCESS_KEY_ID"
    echo "Secret Access Key: $SECRET_ACCESS_KEY"
    
    # Crear archivo .env automáticamente
    echo ""
    echo -e "${YELLOW}📝 Creando archivo .env para el backend...${NC}"
    
    cat > "../backend/.env" << EOF
# Configuración AWS S3 (generado automáticamente)
AWS_ACCESS_KEY_ID=$ACCESS_KEY_ID
AWS_SECRET_ACCESS_KEY=$SECRET_ACCESS_KEY
AWS_REGION=$REGION
S3_BUCKET_NAME=$BUCKET_NAME_OUTPUT

# Configuración del servidor
PORT=3000
NODE_ENV=$ENVIRONMENT
EOF
    
    echo -e "${GREEN}✅ Archivo .env creado en backend/.env${NC}"
    
    echo ""
    echo -e "${BLUE}🎉 ¡Configuración completada!${NC}"
    echo ""
    echo -e "${YELLOW}📋 Próximos pasos:${NC}"
    echo "1. Verificar que el archivo backend/.env se creó correctamente"
    echo "2. Iniciar el backend: cd backend && npm start"
    echo "3. Ejecutar la app Flutter: cd frontend && flutter run"
    echo ""
    echo -e "${YELLOW}⚠️  Importante:${NC}"
    echo "- Guarda las credenciales de forma segura"
    echo "- El bucket está configurado para acceso público de lectura"
    echo "- Las imágenes se almacenan en la carpeta 'images/' del bucket"
    
else
    echo -e "${RED}❌ Error al desplegar el stack${NC}"
    echo "Revisa los logs de CloudFormation en la consola de AWS"
    exit 1
fi 