import 'package:flutter/material.dart';
import '../widgets/simple_image_upload_widget.dart';

class TestS3Screen extends StatefulWidget {
  const TestS3Screen({super.key});

  @override
  State<TestS3Screen> createState() => _TestS3ScreenState();
}

class _TestS3ScreenState extends State<TestS3Screen> {
  String? _profileImageUrl;
  String? _vehicleImageUrl;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test S3 Upload'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const Text(
              'Prueba de Subida de Imágenes a S3',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            
            // Foto de perfil
            const Text(
              'Foto de Perfil',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            SimpleImageUploadWidget(
              currentImageUrl: _profileImageUrl,
              imageType: 'profile',
              onImageUploaded: (imageUrl) {
                setState(() {
                  _profileImageUrl = imageUrl;
                });
                print('✅ Foto de perfil actualizada: $imageUrl');
              },
              onError: (error) {
                print('❌ Error en foto de perfil: $error');
              },
              size: 120,
              isCircular: true,
              placeholder: 'Foto de perfil',
            ),
            
            const SizedBox(height: 32),
            
            // Foto de vehículo
            const Text(
              'Foto de Vehículo',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            SimpleImageUploadWidget(
              currentImageUrl: _vehicleImageUrl,
              imageType: 'vehicle',
              onImageUploaded: (imageUrl) {
                setState(() {
                  _vehicleImageUrl = imageUrl;
                });
                print('✅ Foto de vehículo actualizada: $imageUrl');
              },
              onError: (error) {
                print('❌ Error en foto de vehículo: $error');
              },
              size: 120,
              isCircular: false,
              placeholder: 'Foto del vehículo',
            ),
            
            const SizedBox(height: 32),
            
            // Información de URLs
            if (_profileImageUrl != null || _vehicleImageUrl != null) ...[
              const Divider(),
              const Text(
                'URLs Generadas:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              
              if (_profileImageUrl != null) ...[
                const Text('Perfil:', style: TextStyle(fontWeight: FontWeight.bold)),
                SelectableText(
                  _profileImageUrl!,
                  style: const TextStyle(fontSize: 12),
                ),
                const SizedBox(height: 8),
              ],
              
              if (_vehicleImageUrl != null) ...[
                const Text('Vehículo:', style: TextStyle(fontWeight: FontWeight.bold)),
                SelectableText(
                  _vehicleImageUrl!,
                  style: const TextStyle(fontSize: 12),
                ),
              ],
            ],
            
            const Spacer(),
            
            const Text(
              'Instrucciones:\n'
              '1. Asegúrate de estar logueado\n'
              '2. Toca las imágenes para subir fotos\n'
              '3. Revisa los logs en la consola\n'
              '4. Las URLs aparecerán abajo',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
