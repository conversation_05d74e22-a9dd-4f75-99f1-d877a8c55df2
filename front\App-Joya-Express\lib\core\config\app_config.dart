/**
 * Archivo de configuración de la aplicación.
 * Aquí definimos la URL base de la API y otros parámetros de configuración
 */
class AppConfig {
  // Configuración para desarrollo local con IPv4
  static const String _defaultBaseUrl =
      'http://********:3000/'; // Para emulador Android

  // Ejemplos de configuración según tu setup:
  // Para emulador Android: 'http://********:3000/'
  // Para dispositivo físico: 'http://192.168.1.XXX:3000/' (tu IPv4)
  // Para iOS simulator: 'http://localhost:3000/'

  static String get baseUrl {
    // Aquí podrías implementar lógica para obtener la URL desde:
    // - Variables de entorno
    // - Archivo de configuración
    // - API de configuración
    // - etc.
    return _defaultBaseUrl;
  }

  static const Duration connectTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 60);
  static const Duration sendTimeout = Duration(seconds: 60);
}
