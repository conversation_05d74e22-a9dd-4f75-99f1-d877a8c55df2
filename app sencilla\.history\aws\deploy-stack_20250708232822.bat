@echo off
setlocal enabledelayedexpansion

echo 🚀 Desplegando Stack CloudFormation para App Sencilla
echo ==================================================

REM Verificar si AWS CLI está instalado
aws --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ AWS CLI no está instalado. Por favor instala AWS CLI primero.
    echo Instrucciones: https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html
    pause
    exit /b 1
)

REM Verificar si el usuario está autenticado
aws sts get-caller-identity >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ No estás autenticado en AWS CLI.
    echo Ejecuta: aws configure
    pause
    exit /b 1
)

REM Configuración
set STACK_NAME=app-sencilla-stack
set TEMPLATE_FILE=s3-stack.yaml
for /f "tokens=*" %%i in ('aws configure get region 2^>nul') do set REGION=%%i
if "!REGION!"=="" set REGION=us-east-1

echo 📋 Configuración:
echo Stack Name: %STACK_NAME%
echo Template: %TEMPLATE_FILE%
echo Region: %REGION%
echo.

REM Solicitar nombre del bucket
set /p BUCKET_NAME="Nombre del bucket S3 (debe ser único globalmente): "
if "!BUCKET_NAME!"=="" (
    for /f "tokens=1-3 delims=: " %%a in ('echo %time%') do set TIMESTAMP=%%a%%b%%c
    set BUCKET_NAME=app-sencilla-images-bucket-!TIMESTAMP!
    echo Usando nombre por defecto: !BUCKET_NAME!
)

REM Solicitar ambiente
set /p ENVIRONMENT="Ambiente (development/staging/production) [development]: "
if "!ENVIRONMENT!"=="" set ENVIRONMENT=development

echo.
echo 🔧 Desplegando stack...

REM Desplegar stack
aws cloudformation deploy ^
    --template-file "%TEMPLATE_FILE%" ^
    --stack-name "%STACK_NAME%" ^
    --parameter-overrides ^
        BucketName="!BUCKET_NAME!" ^
        Environment="!ENVIRONMENT!" ^
    --capabilities CAPABILITY_NAMED_IAM ^
    --region "%REGION%"

if %errorlevel% equ 0 (
    echo ✅ Stack desplegado exitosamente!
    
    echo.
    echo 📊 Información del stack:
    
    REM Obtener outputs
    for /f "tokens=*" %%i in ('aws cloudformation describe-stacks --stack-name "%STACK_NAME%" --region "%REGION%" --query "Stacks[0].Outputs" --output json') do set OUTPUTS=%%i
    
    REM Parsear outputs (simplificado para Windows)
    for /f "tokens=*" %%i in ('aws cloudformation describe-stacks --stack-name "%STACK_NAME%" --region "%REGION%" --query "Stacks[0].Outputs[?OutputKey=='BucketName'].OutputValue" --output text') do set BUCKET_NAME_OUTPUT=%%i
    
    for /f "tokens=*" %%i in ('aws cloudformation describe-stacks --stack-name "%STACK_NAME%" --region "%REGION%" --query "Stacks[0].Outputs[?OutputKey=='AccessKeyId'].OutputValue" --output text') do set ACCESS_KEY_ID=%%i
    
    for /f "tokens=*" %%i in ('aws cloudformation describe-stacks --stack-name "%STACK_NAME%" --region "%REGION%" --query "Stacks[0].Outputs[?OutputKey=='SecretAccessKey'].OutputValue" --output text') do set SECRET_ACCESS_KEY=%%i
    
    echo Bucket Name: !BUCKET_NAME_OUTPUT!
    echo Access Key ID: !ACCESS_KEY_ID!
    echo Secret Access Key: !SECRET_ACCESS_KEY!
    
    REM Crear archivo .env automáticamente
    echo.
    echo 📝 Creando archivo .env para el backend...
    
    (
        echo # Configuración AWS S3 (generado automáticamente)
        echo AWS_ACCESS_KEY_ID=!ACCESS_KEY_ID!
        echo AWS_SECRET_ACCESS_KEY=!SECRET_ACCESS_KEY!
        echo AWS_REGION=%REGION%
        echo S3_BUCKET_NAME=!BUCKET_NAME_OUTPUT!
        echo.
        echo # Configuración del servidor
        echo PORT=3000
        echo NODE_ENV=!ENVIRONMENT!
    ) > "..\backend\.env"
    
    echo ✅ Archivo .env creado en backend\.env
    
    echo.
    echo 🎉 ¡Configuración completada!
    echo.
    echo 📋 Próximos pasos:
    echo 1. Verificar que el archivo backend\.env se creó correctamente
    echo 2. Iniciar el backend: cd backend ^&^& npm start
    echo 3. Ejecutar la app Flutter: cd frontend ^&^& flutter run
    echo.
    echo ⚠️  Importante:
    echo - Guarda las credenciales de forma segura
    echo - El bucket está configurado para acceso público de lectura
    echo - Las imágenes se almacenan en la carpeta 'images/' del bucket
    
) else (
    echo ❌ Error al desplegar el stack
    echo Revisa los logs de CloudFormation en la consola de AWS
)

pause 