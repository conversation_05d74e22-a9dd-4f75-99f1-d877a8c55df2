import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:image_picker/image_picker.dart';

class AppImageProvider extends ChangeNotifier {
  File? _selectedImage;
  String? _uploadedImageUrl;
  String? _processedImageUrl;
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  File? get selectedImage => _selectedImage;
  String? get uploadedImageUrl => _uploadedImageUrl;
  String? get processedImageUrl => _processedImageUrl;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // URL del backend (cambiar según tu configuración)
  static const String baseUrl = 'http://localhost:3001'; // Para Windows y Web
  // static const String baseUrl = 'http://********:3001'; // Para emulador Android
  // static const String baseUrl = 'http://localhost:3001'; // Para iOS simulator

  // Seleccionar imagen desde galería
  Future<void> pickImageFromGallery() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        _selectedImage = File(image.path);
        _errorMessage = null;
        notifyListeners();
      }
    } catch (e) {
      _errorMessage = 'Error al seleccionar imagen: $e';
      notifyListeners();
    }
  }

  // Seleccionar imagen desde cámara
  Future<void> pickImageFromCamera() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        _selectedImage = File(image.path);
        _errorMessage = null;
        notifyListeners();
      }
    } catch (e) {
      _errorMessage = 'Error al tomar foto: $e';
      notifyListeners();
    }
  }

  // Subir imagen al backend
  Future<void> uploadImage() async {
    if (_selectedImage == null) {
      _errorMessage = 'No hay imagen seleccionada';
      notifyListeners();
      return;
    }

    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      // Crear request multipart
      var request = http.MultipartRequest('POST', Uri.parse('$baseUrl/upload'));

      // Agregar archivo
      request.files.add(
        await http.MultipartFile.fromPath('image', _selectedImage!.path),
      );

      // Enviar request
      var response = await request.send();
      var responseData = await response.stream.bytesToString();
      var jsonResponse = json.decode(responseData);

      if (response.statusCode == 200) {
        _uploadedImageUrl = jsonResponse['imageUrl'];
        _errorMessage = null;

        // Procesar la imagen automáticamente
        await processImage();
      } else {
        _errorMessage = 'Error al subir imagen: ${jsonResponse['error']}';
      }
    } catch (e) {
      _errorMessage = 'Error de conexión: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Procesar imagen en el backend
  Future<void> processImage() async {
    if (_uploadedImageUrl == null) {
      _errorMessage = 'No hay imagen subida para procesar';
      notifyListeners();
      return;
    }

    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      var response = await http.post(
        Uri.parse('$baseUrl/process-image'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({'imageUrl': _uploadedImageUrl}),
      );

      var jsonResponse = json.decode(response.body);

      if (response.statusCode == 200) {
        _processedImageUrl = jsonResponse['processedUrl'];
        _errorMessage = null;
      } else {
        _errorMessage = 'Error al procesar imagen: ${jsonResponse['error']}';
      }
    } catch (e) {
      _errorMessage = 'Error de conexión: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Limpiar estado
  void clearState() {
    _selectedImage = null;
    _uploadedImageUrl = null;
    _processedImageUrl = null;
    _errorMessage = null;
    notifyListeners();
  }

  // Probar conexión con el backend
  Future<bool> testConnection() async {
    try {
      var response = await http.get(Uri.parse('$baseUrl/'));
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }
}
