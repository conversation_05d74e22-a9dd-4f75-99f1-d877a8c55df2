import 'dart:io';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:http/http.dart' as http;
import '../../core/network/api_client.dart';
import '../../core/network/api_exceptions.dart';

class UploadService {
  final ApiClient _apiClient;

  UploadService(this._apiClient);

  /// Generar URL firmada para subir imagen
  Future<UploadUrlResponse> generateUploadUrl({
    required String fileType,
    required String folder,
  }) async {
    try {
      final response = await _apiClient.post(
        '/uploads/generate-url',
        data: {'fileType': fileType, 'folder': folder},
      );

      if (response.data['success'] == true) {
        return UploadUrlResponse.fromJson(response.data['data']);
      } else {
        throw ServerException(
          message: response.data['message'] ?? 'Error generando URL de subida',
        );
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 400) {
        throw ValidationException(
          message: e.response?.data['message'] ?? 'Datos inválidos',
        );
      } else if (e.response?.statusCode == 503) {
        throw ServerException(
          message: 'Servicio de almacenamiento no disponible',
        );
      }
      throw NetworkException(message: 'Error de conexión: ${e.message}');
    } catch (e) {
      throw ApiException(message: 'Error inesperado: $e');
    }
  }

  /// Subir archivo directamente a S3
  Future<bool> uploadFileToS3({
    required String uploadUrl,
    required File file,
    required String contentType,
  }) async {
    try {
      final bytes = await file.readAsBytes();

      final response = await http.put(
        Uri.parse(uploadUrl),
        body: bytes,
        headers: {'Content-Type': contentType},
      );

      return response.statusCode == 200;
    } catch (e) {
      throw NetworkException(message: 'Error subiendo archivo: $e');
    }
  }

  /// Subir archivo desde bytes (para web)
  Future<bool> uploadBytesToS3({
    required String uploadUrl,
    required Uint8List bytes,
    required String contentType,
  }) async {
    try {
      final response = await http.put(
        Uri.parse(uploadUrl),
        body: bytes,
        headers: {'Content-Type': contentType},
      );

      return response.statusCode == 200;
    } catch (e) {
      throw NetworkException(message: 'Error subiendo archivo: $e');
    }
  }

  /// Confirmar que el archivo fue subido correctamente
  Future<bool> confirmUpload({
    required String key,
    required String publicUrl,
  }) async {
    try {
      final response = await _apiClient.post(
        '/uploads/confirm',
        data: {'key': key, 'publicUrl': publicUrl},
      );

      return response.data['success'] == true;
    } on DioException catch (e) {
      if (e.response?.statusCode == 404) {
        throw NotFoundException(
          message: 'Archivo no encontrado en el almacenamiento',
        );
      }
      throw NetworkException(message: 'Error confirmando subida: ${e.message}');
    } catch (e) {
      throw UnknownException(message: 'Error inesperado: $e');
    }
  }

  /// Eliminar imagen
  Future<bool> deleteImage(String key) async {
    try {
      final response = await _apiClient.delete('/uploads/$key');
      return response.data['success'] == true;
    } on DioException catch (e) {
      if (e.response?.statusCode == 403) {
        throw UnauthorizedException(
          message: 'No tienes permisos para eliminar este archivo',
        );
      } else if (e.response?.statusCode == 404) {
        throw NotFoundException(message: 'Archivo no encontrado');
      }
      throw NetworkException(message: 'Error eliminando archivo: ${e.message}');
    } catch (e) {
      throw UnknownException(message: 'Error inesperado: $e');
    }
  }

  /// Obtener información del servicio de almacenamiento
  Future<StorageInfo> getStorageInfo() async {
    try {
      final response = await _apiClient.get('/uploads/info');

      if (response.data['success'] == true) {
        return StorageInfo.fromJson(response.data['data']);
      } else {
        throw ServerException(
          message: 'Error obteniendo información del servicio',
        );
      }
    } on DioException catch (e) {
      throw NetworkException(message: 'Error de conexión: ${e.message}');
    } catch (e) {
      throw UnknownException(message: 'Error inesperado: $e');
    }
  }

  /// Proceso completo de subida de imagen
  Future<String> uploadImage({
    required File file,
    required String folder,
  }) async {
    try {
      print('🔄 [UPLOAD] Iniciando subida de imagen...');
      print('📁 [UPLOAD] Archivo: ${file.path}');
      print('📂 [UPLOAD] Carpeta: $folder');

      // 1. Determinar tipo de contenido
      final contentType = _getContentType(file.path);
      print('📄 [UPLOAD] Tipo de contenido: $contentType');

      // 2. Generar URL firmada
      print('🔗 [UPLOAD] Generando URL firmada...');
      final uploadData = await generateUploadUrl(
        fileType: contentType,
        folder: folder,
      );
      print('✅ [UPLOAD] URL firmada generada exitosamente');
      print('🔑 [UPLOAD] Key: ${uploadData.key}');

      // 3. Subir archivo a S3
      print('☁️ [UPLOAD] Subiendo archivo a S3...');
      final uploadSuccess = await uploadFileToS3(
        uploadUrl: uploadData.uploadUrl,
        file: file,
        contentType: contentType,
      );

      if (!uploadSuccess) {
        print('❌ [UPLOAD] Error: Subida a S3 falló');
        throw ServerException(message: 'Error subiendo archivo a S3');
      }
      print('✅ [UPLOAD] Archivo subido a S3 exitosamente');

      // 4. Confirmar subida
      print('✔️ [UPLOAD] Confirmando subida...');
      await confirmUpload(key: uploadData.key, publicUrl: uploadData.publicUrl);
      print('✅ [UPLOAD] Subida confirmada exitosamente');

      // 5. Retornar URL pública
      print('🎉 [UPLOAD] Proceso completado. URL: ${uploadData.publicUrl}');
      return uploadData.publicUrl;
    } catch (e) {
      print('❌ [UPLOAD] Error en uploadImage: $e');
      rethrow;
    }
  }

  /// Proceso completo de subida desde bytes
  Future<String> uploadImageFromBytes({
    required Uint8List bytes,
    required String fileName,
    required String folder,
  }) async {
    try {
      // 1. Determinar tipo de contenido
      final contentType = _getContentTypeFromFileName(fileName);

      // 2. Generar URL firmada
      final uploadData = await generateUploadUrl(
        fileType: contentType,
        folder: folder,
      );

      // 3. Subir bytes a S3
      final uploadSuccess = await uploadBytesToS3(
        uploadUrl: uploadData.uploadUrl,
        bytes: bytes,
        contentType: contentType,
      );

      if (!uploadSuccess) {
        throw ServerException(message: 'Error subiendo archivo a S3');
      }

      // 4. Confirmar subida
      await confirmUpload(key: uploadData.key, publicUrl: uploadData.publicUrl);

      // 5. Retornar URL pública
      return uploadData.publicUrl;
    } catch (e) {
      rethrow;
    }
  }

  /// Determinar tipo de contenido basado en la extensión del archivo
  String _getContentType(String filePath) {
    final extension = filePath.toLowerCase().split('.').last;
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'webp':
        return 'image/webp';
      default:
        return 'image/jpeg';
    }
  }

  /// Determinar tipo de contenido basado en el nombre del archivo
  String _getContentTypeFromFileName(String fileName) {
    final extension = fileName.toLowerCase().split('.').last;
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'webp':
        return 'image/webp';
      default:
        return 'image/jpeg';
    }
  }
}

/// Modelo para la respuesta de URL de subida
class UploadUrlResponse {
  final String uploadUrl;
  final String publicUrl;
  final String key;
  final int expiresIn;

  UploadUrlResponse({
    required this.uploadUrl,
    required this.publicUrl,
    required this.key,
    required this.expiresIn,
  });

  factory UploadUrlResponse.fromJson(Map<String, dynamic> json) {
    return UploadUrlResponse(
      uploadUrl: json['uploadUrl'] as String,
      publicUrl: json['publicUrl'] as String,
      key: json['key'] as String,
      expiresIn: json['expiresIn'] as int,
    );
  }
}

/// Modelo para información del servicio de almacenamiento
class StorageInfo {
  final String service;
  final String bucket;
  final String region;
  final bool configured;
  final List<String> supportedFormats;
  final String maxFileSize;
  final String urlExpiration;

  StorageInfo({
    required this.service,
    required this.bucket,
    required this.region,
    required this.configured,
    required this.supportedFormats,
    required this.maxFileSize,
    required this.urlExpiration,
  });

  factory StorageInfo.fromJson(Map<String, dynamic> json) {
    return StorageInfo(
      service: json['service'] as String,
      bucket: json['bucket'] as String,
      region: json['region'] as String,
      configured: json['configured'] as bool,
      supportedFormats: List<String>.from(json['supportedFormats']),
      maxFileSize: json['maxFileSize'] as String,
      urlExpiration: json['urlExpiration'] as String,
    );
  }
}
