const express = require('express');
const imageUploadController = require('../controllers/image-upload.controller');
const { authenticateAccessToken } = require('../middleware/auth.middleware');
const { authenticateConductorToken } = require('../middleware/conductor-auth.middleware');

const router = express.Router();

// Middleware para autenticación (usuarios o conductores)
const authenticateUser = (req, res, next) => {
    // Intentar autenticar como usuario primero
    authenticateAccessToken(req, res, (err) => {
        if (err || !req.user) {
            // Si falla, intentar como conductor
            authenticateConductorToken(req, res, (err2) => {
                if (err2 || !req.user) {
                    return res.status(401).json({
                        success: false,
                        message: 'Token de autenticación requerido'
                    });
                }
                next();
            });
        } else {
            next();
        }
    });
};

/**
 * POST /api/uploads/image
 * Subir imagen (perfil de usuario/conductor o vehículo)
 * 
 * Body (multipart/form-data):
 * - image: archivo de imagen
 * - type: 'profile' | 'vehicle' (opcional, default: 'profile')
 * 
 * Response:
 * {
 *   "success": true,
 *   "url": "https://s3-bucket-url/path/to/image.jpg",
 *   "message": "Imagen subida exitosamente"
 * }
 */
router.post('/image', 
    authenticateUser,
    imageUploadController.constructor.getUploadMiddleware(),
    imageUploadController.uploadImage.bind(imageUploadController)
);

module.exports = router;
