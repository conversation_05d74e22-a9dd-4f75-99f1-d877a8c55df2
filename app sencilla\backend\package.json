{"name": "app-sencilla-backend", "version": "1.0.0", "description": "Backend para procesar imágenes desde S3", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "aws-sdk": "^2.1450.0", "multer": "^1.4.5-lts.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["flutter", "aws", "s3", "backend"], "author": "Tu Nombre", "license": "MIT"}