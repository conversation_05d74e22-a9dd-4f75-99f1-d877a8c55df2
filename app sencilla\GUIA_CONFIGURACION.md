# Guía de Configuración - App Sencilla

## 📋 Requisitos Previos

### 1. Node.js
- Instalar Node.js desde [nodejs.org](https://nodejs.org/)
- Versión recomendada: 18.x o superior

### 2. Flutter SDK
- Instalar Flutter desde [flutter.dev](https://flutter.dev/)
- Configurar variables de entorno
- Ejecutar `flutter doctor` para verificar la instalación

### 3. AWS Account
- Crear cuenta en [aws.amazon.com](https://aws.amazon.com/)
- Configurar un bucket S3
- Crear usuario IAM con permisos de S3

## 🔧 Configuración AWS S3

### 1. Crear Bucket S3
1. Ir a AWS Console → S3
2. Crear bucket con nombre único
3. Configurar región (ej: us-east-1)
4. **IMPORTANTE**: Configurar bucket como público para lectura

### 2. Configurar Política de Bucket
```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "PublicReadGetObject",
            "Effect": "Allow",
            "Principal": "*",
            "Action": "s3:GetObject",
            "Resource": "arn:aws:s3:::TU-BUCKET-NAME/*"
        }
    ]
}
```

### 3. Crear Usuario IAM
1. Ir a AWS Console → IAM
2. Crear usuario con acceso programático
3. Adjuntar política `AmazonS3FullAccess` (o crear política personalizada)
4. Guardar Access Key ID y Secret Access Key

## 🚀 Instalación del Proyecto

### Opción 1: Script Automático
```bash
# Linux/Mac
chmod +x setup.sh
./setup.sh

# Windows
setup.bat
```

### Opción 2: Instalación Manual

#### Backend
```bash
cd backend
npm install
```

#### Frontend
```bash
cd frontend
flutter pub get
```

## ⚙️ Configuración de Variables de Entorno

### 1. Crear archivo `.env` en el directorio `backend/`
```env
# Configuración AWS S3
AWS_ACCESS_KEY_ID=tu_access_key_aqui
AWS_SECRET_ACCESS_KEY=tu_secret_key_aqui
AWS_REGION=us-east-1
S3_BUCKET_NAME=tu-bucket-nombre

# Configuración del servidor
PORT=3000
NODE_ENV=development
```

### 2. Verificar configuración en Flutter
En `frontend/lib/providers/image_provider.dart`, verificar la URL del backend:
```dart
// Para emulador Android
static const String baseUrl = 'http://********:3000';

// Para iOS simulator
// static const String baseUrl = 'http://localhost:3000';

// Para dispositivo físico (cambiar por IP de tu computadora)
// static const String baseUrl = 'http://*************:3000';
```

## 🏃‍♂️ Ejecutar la Aplicación

### 1. Iniciar Backend
```bash
cd backend
npm start
```

Deberías ver:
```
🚀 Servidor corriendo en http://localhost:3000
📱 Backend listo para conectar con Flutter
☁️  Configurado para AWS S3
```

### 2. Ejecutar App Flutter
```bash
cd frontend
flutter run
```

## 📱 Funcionalidades de la App

### Flujo de Trabajo
1. **Seleccionar Imagen**: Usar botones "Galería" o "Cámara"
2. **Subir a S3**: La imagen se sube automáticamente al bucket S3
3. **Procesar**: El backend recibe la URL y la procesa
4. **Mostrar Resultado**: La imagen procesada se muestra en la app

### Características
- ✅ Selección de imágenes desde galería o cámara
- ✅ Subida automática a AWS S3
- ✅ Procesamiento en backend
- ✅ Visualización de URLs
- ✅ Indicador de estado de conexión
- ✅ Manejo de errores
- ✅ Interfaz moderna y responsive

## 🔍 Troubleshooting

### Problemas Comunes

#### 1. Error de Conexión
- Verificar que el backend esté corriendo en puerto 3000
- Verificar URL en `image_provider.dart`
- Para dispositivos físicos, usar IP de la computadora

#### 2. Error de Permisos AWS
- Verificar credenciales en `.env`
- Verificar permisos del usuario IAM
- Verificar que el bucket sea público

#### 3. Error de Permisos en Flutter
- Verificar `AndroidManifest.xml` para Android
- Verificar `Info.plist` para iOS
- Solicitar permisos manualmente si es necesario

#### 4. Error de CORS
- El backend ya incluye configuración CORS
- Verificar que no haya firewall bloqueando el puerto

### Logs Útiles
```bash
# Backend logs
cd backend && npm start

# Flutter logs
cd frontend && flutter run --verbose
```

## 📚 Recursos Adicionales

- [Documentación Flutter](https://docs.flutter.dev/)
- [Documentación AWS S3](https://docs.aws.amazon.com/s3/)
- [Documentación Express.js](https://expressjs.com/)
- [Guía de Permisos Android](https://developer.android.com/training/permissions/requesting)
- [Guía de Permisos iOS](https://developer.apple.com/documentation/security/requesting_authorization_to_access_photos)

## 🤝 Contribuir

1. Fork el proyecto
2. Crear rama para nueva funcionalidad
3. Commit los cambios
4. Push a la rama
5. Crear Pull Request

## 📄 Licencia

Este proyecto está bajo la licencia MIT. 