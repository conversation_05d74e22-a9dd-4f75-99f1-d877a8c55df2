import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../../data/services/simple_image_upload_service.dart';
import '../../core/di/service_locator.dart';

class SimpleImageUploadWidget extends StatefulWidget {
  final String? currentImageUrl;
  final String imageType; // 'profile' o 'vehicle'
  final Function(String imageUrl) onImageUploaded;
  final Function(String error)? onError;
  final double size;
  final bool isCircular;
  final String? placeholder;

  const SimpleImageUploadWidget({
    super.key,
    this.currentImageUrl,
    required this.imageType,
    required this.onImageUploaded,
    this.onError,
    this.size = 120,
    this.isCircular = true,
    this.placeholder,
  });

  @override
  State<SimpleImageUploadWidget> createState() => _SimpleImageUploadWidgetState();
}

class _SimpleImageUploadWidgetState extends State<SimpleImageUploadWidget> {
  final ImagePicker _picker = ImagePicker();
  final SimpleImageUploadService _uploadService = sl<SimpleImageUploadService>();
  
  bool _isUploading = false;
  String? _currentImageUrl;

  @override
  void initState() {
    super.initState();
    _currentImageUrl = widget.currentImageUrl;
  }

  Future<void> _pickAndUploadImage(ImageSource source) async {
    try {
      print('🔄 [SIMPLE-WIDGET] Iniciando selección de imagen...');
      
      final XFile? pickedFile = await _picker.pickImage(
        source: source,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (pickedFile == null) {
        print('❌ [SIMPLE-WIDGET] No se seleccionó ninguna imagen');
        return;
      }

      print('✅ [SIMPLE-WIDGET] Imagen seleccionada: ${pickedFile.path}');
      print('🖼️ [SIMPLE-WIDGET] Tipo: ${widget.imageType}');

      setState(() {
        _isUploading = true;
      });

      // Subir imagen usando nuestro servicio simple
      final imageUrl = await _uploadService.uploadImage(
        file: File(pickedFile.path),
        type: widget.imageType,
      );

      print('🎉 [SIMPLE-WIDGET] Upload exitoso: $imageUrl');

      setState(() {
        _currentImageUrl = imageUrl;
        _isUploading = false;
      });

      widget.onImageUploaded(imageUrl);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Imagen subida correctamente'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      print('❌ [SIMPLE-WIDGET] Error en upload: $e');
      
      setState(() {
        _isUploading = false;
      });

      final errorMessage = e.toString();
      widget.onError?.call(errorMessage);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $errorMessage'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showImageSourceDialog() {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Wrap(
            children: [
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('Galería'),
                onTap: () {
                  Navigator.of(context).pop();
                  _pickAndUploadImage(ImageSource.gallery);
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_camera),
                title: const Text('Cámara'),
                onTap: () {
                  Navigator.of(context).pop();
                  _pickAndUploadImage(ImageSource.camera);
                },
              ),
              if (_currentImageUrl != null)
                ListTile(
                  leading: const Icon(Icons.delete, color: Colors.red),
                  title: const Text('Eliminar imagen'),
                  onTap: () {
                    Navigator.of(context).pop();
                    _removeImage();
                  },
                ),
            ],
          ),
        );
      },
    );
  }

  void _removeImage() {
    setState(() {
      _currentImageUrl = null;
    });
    widget.onImageUploaded(''); // Enviar string vacío para indicar eliminación
  }

  Widget _buildPlaceholder() {
    return Container(
      width: widget.size,
      height: widget.size,
      decoration: BoxDecoration(
        shape: widget.isCircular ? BoxShape.circle : BoxShape.rectangle,
        borderRadius: widget.isCircular ? null : BorderRadius.circular(12),
        color: Colors.grey.shade200,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.add_a_photo,
            size: widget.size * 0.3,
            color: Colors.grey.shade600,
          ),
          if (widget.placeholder != null) ...[
            const SizedBox(height: 4),
            Text(
              widget.placeholder!,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        GestureDetector(
          onTap: _showImageSourceDialog,
          child: Container(
            width: widget.size,
            height: widget.size,
            decoration: BoxDecoration(
              shape: widget.isCircular ? BoxShape.circle : BoxShape.rectangle,
              borderRadius: widget.isCircular ? null : BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade300, width: 2),
              color: Colors.grey.shade100,
            ),
            child: _isUploading
                ? const Center(child: CircularProgressIndicator())
                : _currentImageUrl != null && _currentImageUrl!.isNotEmpty
                    ? ClipRRect(
                        borderRadius: widget.isCircular 
                            ? BorderRadius.circular(widget.size / 2)
                            : BorderRadius.circular(10),
                        child: Image.network(
                          _currentImageUrl!,
                          width: widget.size,
                          height: widget.size,
                          fit: BoxFit.cover,
                          loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) return child;
                            return Center(
                              child: CircularProgressIndicator(
                                value: loadingProgress.expectedTotalBytes != null
                                    ? loadingProgress.cumulativeBytesLoaded /
                                        loadingProgress.expectedTotalBytes!
                                    : null,
                              ),
                            );
                          },
                          errorBuilder: (context, error, stackTrace) {
                            return _buildPlaceholder();
                          },
                        ),
                      )
                    : _buildPlaceholder(),
          ),
        ),
        const SizedBox(height: 8),
        TextButton.icon(
          onPressed: _isUploading ? null : _showImageSourceDialog,
          icon: const Icon(Icons.camera_alt),
          label: Text(
            _currentImageUrl != null ? 'Cambiar imagen' : 'Subir imagen',
          ),
        ),
      ],
    );
  }
}
