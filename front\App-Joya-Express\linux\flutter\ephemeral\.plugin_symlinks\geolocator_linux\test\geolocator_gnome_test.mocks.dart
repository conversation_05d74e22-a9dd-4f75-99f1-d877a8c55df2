// Mocks generated by Mockito 5.3.2 from annotations
// in geolocator_linux/test/geolocator_gnome_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:dbus/dbus.dart' as _i4;
import 'package:geoclue/src/accuracy_level.dart' as _i2;
import 'package:geoclue/src/geoclue.dart' as _i3;
import 'package:gsettings/src/gsettings.dart' as _i6;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeGeoClueAccuracyLevel_0 extends _i1.SmartFake
    implements _i2.GeoClueAccuracyLevel {
  _FakeGeoClueAccuracyLevel_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeGeoClueClient_1 extends _i1.SmartFake implements _i3.GeoClueClient {
  _FakeGeoClueClient_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDBusValue_2 extends _i1.SmartFake implements _i4.DBusValue {
  _FakeDBusValue_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDBusClient_3 extends _i1.SmartFake implements _i4.DBusClient {
  _FakeDBusClient_3(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDBusObjectPath_4 extends _i1.SmartFake
    implements _i4.DBusObjectPath {
  _FakeDBusObjectPath_4(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDBusIntrospectNode_5 extends _i1.SmartFake
    implements _i4.DBusIntrospectNode {
  _FakeDBusIntrospectNode_5(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDBusMethodSuccessResponse_6 extends _i1.SmartFake
    implements _i4.DBusMethodSuccessResponse {
  _FakeDBusMethodSuccessResponse_6(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [GeoClueManager].
///
/// See the documentation for Mockito's code generation for more information.
class MockGeoClueManager extends _i1.Mock implements _i3.GeoClueManager {
  MockGeoClueManager() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get inUse => (super.noSuchMethod(
        Invocation.getter(#inUse),
        returnValue: false,
      ) as bool);
  @override
  _i2.GeoClueAccuracyLevel get availableAccuracyLevel => (super.noSuchMethod(
        Invocation.getter(#availableAccuracyLevel),
        returnValue: _FakeGeoClueAccuracyLevel_0(
          this,
          Invocation.getter(#availableAccuracyLevel),
        ),
      ) as _i2.GeoClueAccuracyLevel);
  @override
  _i5.Stream<List<String>> get propertiesChanged => (super.noSuchMethod(
        Invocation.getter(#propertiesChanged),
        returnValue: _i5.Stream<List<String>>.empty(),
      ) as _i5.Stream<List<String>>);
  @override
  _i5.Future<void> connect() => (super.noSuchMethod(
        Invocation.method(
          #connect,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);
  @override
  _i5.Future<void> close() => (super.noSuchMethod(
        Invocation.method(
          #close,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);
  @override
  _i5.Future<_i3.GeoClueClient> getClient() => (super.noSuchMethod(
        Invocation.method(
          #getClient,
          [],
        ),
        returnValue: _i5.Future<_i3.GeoClueClient>.value(_FakeGeoClueClient_1(
          this,
          Invocation.method(
            #getClient,
            [],
          ),
        )),
      ) as _i5.Future<_i3.GeoClueClient>);
  @override
  _i5.Future<_i3.GeoClueClient> createClient() => (super.noSuchMethod(
        Invocation.method(
          #createClient,
          [],
        ),
        returnValue: _i5.Future<_i3.GeoClueClient>.value(_FakeGeoClueClient_1(
          this,
          Invocation.method(
            #createClient,
            [],
          ),
        )),
      ) as _i5.Future<_i3.GeoClueClient>);
  @override
  _i5.Future<void> deleteClient(_i3.GeoClueClient? client) =>
      (super.noSuchMethod(
        Invocation.method(
          #deleteClient,
          [client],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);
}

/// A class which mocks [GSettings].
///
/// See the documentation for Mockito's code generation for more information.
class MockGSettings extends _i1.Mock implements _i6.GSettings {
  MockGSettings() {
    _i1.throwOnMissingStub(this);
  }

  @override
  String get schemaName => (super.noSuchMethod(
        Invocation.getter(#schemaName),
        returnValue: '',
      ) as String);
  @override
  _i5.Stream<List<String>> get keysChanged => (super.noSuchMethod(
        Invocation.getter(#keysChanged),
        returnValue: _i5.Stream<List<String>>.empty(),
      ) as _i5.Stream<List<String>>);
  @override
  _i5.Future<List<String>> list() => (super.noSuchMethod(
        Invocation.method(
          #list,
          [],
        ),
        returnValue: _i5.Future<List<String>>.value(<String>[]),
      ) as _i5.Future<List<String>>);
  @override
  _i5.Future<_i4.DBusValue> get(String? name) => (super.noSuchMethod(
        Invocation.method(
          #get,
          [name],
        ),
        returnValue: _i5.Future<_i4.DBusValue>.value(_FakeDBusValue_2(
          this,
          Invocation.method(
            #get,
            [name],
          ),
        )),
      ) as _i5.Future<_i4.DBusValue>);
  @override
  _i5.Future<_i4.DBusValue> getDefault(String? name) => (super.noSuchMethod(
        Invocation.method(
          #getDefault,
          [name],
        ),
        returnValue: _i5.Future<_i4.DBusValue>.value(_FakeDBusValue_2(
          this,
          Invocation.method(
            #getDefault,
            [name],
          ),
        )),
      ) as _i5.Future<_i4.DBusValue>);
  @override
  _i5.Future<bool> isSet(String? name) => (super.noSuchMethod(
        Invocation.method(
          #isSet,
          [name],
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);
  @override
  _i5.Future<void> set(
    String? name,
    _i4.DBusValue? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #set,
          [
            name,
            value,
          ],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);
  @override
  _i5.Future<void> unset(String? name) => (super.noSuchMethod(
        Invocation.method(
          #unset,
          [name],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);
  @override
  _i5.Future<void> setAll(Map<String, _i4.DBusValue?>? values) =>
      (super.noSuchMethod(
        Invocation.method(
          #setAll,
          [values],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);
  @override
  _i5.Future<void> close() => (super.noSuchMethod(
        Invocation.method(
          #close,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);
}

/// A class which mocks [DBusRemoteObject].
///
/// See the documentation for Mockito's code generation for more information.
class MockDBusRemoteObject extends _i1.Mock implements _i4.DBusRemoteObject {
  MockDBusRemoteObject() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.DBusClient get client => (super.noSuchMethod(
        Invocation.getter(#client),
        returnValue: _FakeDBusClient_3(
          this,
          Invocation.getter(#client),
        ),
      ) as _i4.DBusClient);
  @override
  String get name => (super.noSuchMethod(
        Invocation.getter(#name),
        returnValue: '',
      ) as String);
  @override
  _i4.DBusObjectPath get path => (super.noSuchMethod(
        Invocation.getter(#path),
        returnValue: _FakeDBusObjectPath_4(
          this,
          Invocation.getter(#path),
        ),
      ) as _i4.DBusObjectPath);
  @override
  _i5.Stream<_i4.DBusPropertiesChangedSignal> get propertiesChanged =>
      (super.noSuchMethod(
        Invocation.getter(#propertiesChanged),
        returnValue: _i5.Stream<_i4.DBusPropertiesChangedSignal>.empty(),
      ) as _i5.Stream<_i4.DBusPropertiesChangedSignal>);
  @override
  set propertiesChanged(
          _i5.Stream<_i4.DBusPropertiesChangedSignal>? _propertiesChanged) =>
      super.noSuchMethod(
        Invocation.setter(
          #propertiesChanged,
          _propertiesChanged,
        ),
        returnValueForMissingStub: null,
      );
  @override
  _i5.Future<_i4.DBusIntrospectNode> introspect() => (super.noSuchMethod(
        Invocation.method(
          #introspect,
          [],
        ),
        returnValue:
            _i5.Future<_i4.DBusIntrospectNode>.value(_FakeDBusIntrospectNode_5(
          this,
          Invocation.method(
            #introspect,
            [],
          ),
        )),
      ) as _i5.Future<_i4.DBusIntrospectNode>);
  @override
  _i5.Future<_i4.DBusValue> getProperty(
    String? interface,
    String? name, {
    _i4.DBusSignature? signature,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getProperty,
          [
            interface,
            name,
          ],
          {#signature: signature},
        ),
        returnValue: _i5.Future<_i4.DBusValue>.value(_FakeDBusValue_2(
          this,
          Invocation.method(
            #getProperty,
            [
              interface,
              name,
            ],
            {#signature: signature},
          ),
        )),
      ) as _i5.Future<_i4.DBusValue>);
  @override
  _i5.Future<Map<String, _i4.DBusValue>> getAllProperties(String? interface) =>
      (super.noSuchMethod(
        Invocation.method(
          #getAllProperties,
          [interface],
        ),
        returnValue: _i5.Future<Map<String, _i4.DBusValue>>.value(
            <String, _i4.DBusValue>{}),
      ) as _i5.Future<Map<String, _i4.DBusValue>>);
  @override
  _i5.Future<void> setProperty(
    String? interface,
    String? name,
    _i4.DBusValue? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #setProperty,
          [
            interface,
            name,
            value,
          ],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);
  @override
  _i5.Future<_i4.DBusMethodSuccessResponse> callMethod(
    String? interface,
    String? name,
    Iterable<_i4.DBusValue>? values, {
    _i4.DBusSignature? replySignature,
    bool? noReplyExpected = false,
    bool? noAutoStart = false,
    bool? allowInteractiveAuthorization = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #callMethod,
          [
            interface,
            name,
            values,
          ],
          {
            #replySignature: replySignature,
            #noReplyExpected: noReplyExpected,
            #noAutoStart: noAutoStart,
            #allowInteractiveAuthorization: allowInteractiveAuthorization,
          },
        ),
        returnValue: _i5.Future<_i4.DBusMethodSuccessResponse>.value(
            _FakeDBusMethodSuccessResponse_6(
          this,
          Invocation.method(
            #callMethod,
            [
              interface,
              name,
              values,
            ],
            {
              #replySignature: replySignature,
              #noReplyExpected: noReplyExpected,
              #noAutoStart: noAutoStart,
              #allowInteractiveAuthorization: allowInteractiveAuthorization,
            },
          ),
        )),
      ) as _i5.Future<_i4.DBusMethodSuccessResponse>);
}
