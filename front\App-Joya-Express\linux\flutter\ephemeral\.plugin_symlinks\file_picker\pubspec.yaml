name: file_picker
description: A package that allows you to use a native file explorer to pick single or multiple absolute file paths, with extension filtering support.
homepage: https://github.com/miguelpruivo/plugins_flutter_file_picker
repository: https://github.com/miguelpruivo/flutter_file_picker
issue_tracker: https://github.com/miguelpruivo/flutter_file_picker/issues
version: 10.2.0

dependencies:
  flutter:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter

  flutter_plugin_android_lifecycle: ^2.0.22
  plugin_platform_interface: ^2.1.8
  ffi: ^2.1.3
  path: ^1.9.0
  win32: ^5.9.0
  cross_file: ^0.3.4+2
  web: ^1.1.0

dev_dependencies:
  lints: ^5.1.1
  flutter_test:
    sdk: flutter

environment:
  sdk: ">=3.4.0 <4.0.0"
  flutter: ">=3.22.0"

flutter:
  plugin:
    platforms:
      android:
        package: com.mr.flutter.plugin.filepicker
        pluginClass: FilePickerPlugin
        dartPluginClass: FilePickerIO
      ios:
        pluginClass: FilePickerPlugin
        dartPluginClass: FilePickerIO
      web:
        pluginClass: FilePickerWeb
        fileName: _internal/file_picker_web.dart
      macos:
        pluginClass: FilePickerPlugin
        dartPluginClass: FilePickerMacOS
      windows:
        dartPluginClass: FilePickerWindows
      linux:
        dartPluginClass: FilePickerLinux
