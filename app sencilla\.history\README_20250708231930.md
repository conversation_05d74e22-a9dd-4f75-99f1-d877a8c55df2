# App Sencilla - Flutter + AWS S3 + Backend

Una aplicación móvil Flutter que permite subir imágenes a AWS S3 y procesarlas a través de un backend.

## Estructura del Proyecto

```
app-sencilla/
├── frontend/          # App Flutter
├── backend/           # API Node.js/Express
└── README.md          # Documentación
```

## Funcionalidades

- 📱 **Frontend Flutter**: Interfaz para seleccionar y subir imágenes
- ☁️ **AWS S3**: Almacenamiento de imágenes en la nube
- 🔧 **Backend**: API para procesar URLs de imágenes
- 🔄 **Comunicación**: Flujo completo de subida y descarga de imágenes

## Tecnologías Utilizadas

- **Frontend**: Flutter, Dart
- **Backend**: Node.js, Express
- **Cloud**: AWS S3
- **Comunicación**: HTTP/REST API

## Setup

### Prerrequisitos
- Flutter SDK
- Node.js
- <PERSON>uenta de AWS con S3 configurado

### Instalación

1. **Backend**:
   ```bash
   cd backend
   npm install
   ```

2. **Frontend**:
   ```bash
   cd frontend
   flutter pub get
   ```

## Configuración AWS S3

Necesitarás configurar las credenciales de AWS en el backend:
- AWS_ACCESS_KEY_ID
- AWS_SECRET_ACCESS_KEY
- AWS_REGION
- S3_BUCKET_NAME

## Uso

1. Inicia el backend: `npm start`
2. Ejecuta la app Flutter: `flutter run`
3. Selecciona una imagen en la app
4. La imagen se subirá a S3 y se mostrará la URL procesada 