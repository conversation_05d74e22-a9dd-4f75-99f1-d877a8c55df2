// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.
// Autogenerated from Pig<PERSON> (v22.6.1), do not edit directly.
// See also: https://pub.dev/packages/pigeon

#ifndef PIGEON_MESSAGES_G_H_
#define PIGEON_MESSAGES_G_H_

#include <flutter_linux/flutter_linux.h>

G_BEGIN_DECLS

G_DECLARE_FINAL_TYPE(FulMessageCodec, ful_message_codec, FUL, MESSAGE_CODEC,
                     FlStandardMessageCodec)

G_DECLARE_FINAL_TYPE(FulUrlLauncherApi, ful_url_launcher_api, FUL,
                     URL_LAUNCHER_API, GObject)

G_DECLARE_FINAL_TYPE(FulUrlLauncherApiCanLaunchUrlResponse,
                     ful_url_launcher_api_can_launch_url_response, FUL,
                     URL_LAUNCHER_API_CAN_LAUNCH_URL_RESPONSE, GObject)

/**
 * ful_url_launcher_api_can_launch_url_response_new:
 *
 * Creates a new response to UrlLauncherApi.canLaunchUrl.
 *
 * Returns: a new #FulUrlLauncherApiCanLaunchUrlResponse
 */
FulUrlLauncherApiCanLaunchUrlResponse*
ful_url_launcher_api_can_launch_url_response_new(gboolean return_value);

/**
 * ful_url_launcher_api_can_launch_url_response_new_error:
 * @code: error code.
 * @message: error message.
 * @details: (allow-none): error details or %NULL.
 *
 * Creates a new error response to UrlLauncherApi.canLaunchUrl.
 *
 * Returns: a new #FulUrlLauncherApiCanLaunchUrlResponse
 */
FulUrlLauncherApiCanLaunchUrlResponse*
ful_url_launcher_api_can_launch_url_response_new_error(const gchar* code,
                                                       const gchar* message,
                                                       FlValue* details);

G_DECLARE_FINAL_TYPE(FulUrlLauncherApiLaunchUrlResponse,
                     ful_url_launcher_api_launch_url_response, FUL,
                     URL_LAUNCHER_API_LAUNCH_URL_RESPONSE, GObject)

/**
 * ful_url_launcher_api_launch_url_response_new:
 *
 * Creates a new response to UrlLauncherApi.launchUrl.
 *
 * Returns: a new #FulUrlLauncherApiLaunchUrlResponse
 */
FulUrlLauncherApiLaunchUrlResponse*
ful_url_launcher_api_launch_url_response_new(const gchar* return_value);

/**
 * ful_url_launcher_api_launch_url_response_new_error:
 * @code: error code.
 * @message: error message.
 * @details: (allow-none): error details or %NULL.
 *
 * Creates a new error response to UrlLauncherApi.launchUrl.
 *
 * Returns: a new #FulUrlLauncherApiLaunchUrlResponse
 */
FulUrlLauncherApiLaunchUrlResponse*
ful_url_launcher_api_launch_url_response_new_error(const gchar* code,
                                                   const gchar* message,
                                                   FlValue* details);

/**
 * FulUrlLauncherApiVTable:
 *
 * Table of functions exposed by UrlLauncherApi to be implemented by the API
 * provider.
 */
typedef struct {
  FulUrlLauncherApiCanLaunchUrlResponse* (*can_launch_url)(const gchar* url,
                                                           gpointer user_data);
  FulUrlLauncherApiLaunchUrlResponse* (*launch_url)(const gchar* url,
                                                    gpointer user_data);
} FulUrlLauncherApiVTable;

/**
 * ful_url_launcher_api_set_method_handlers:
 *
 * @messenger: an #FlBinaryMessenger.
 * @suffix: (allow-none): a suffix to add to the API or %NULL for none.
 * @vtable: implementations of the methods in this API.
 * @user_data: (closure): user data to pass to the functions in @vtable.
 * @user_data_free_func: (allow-none): a function which gets called to free
 * @user_data, or %NULL.
 *
 * Connects the method handlers in the UrlLauncherApi API.
 */
void ful_url_launcher_api_set_method_handlers(
    FlBinaryMessenger* messenger, const gchar* suffix,
    const FulUrlLauncherApiVTable* vtable, gpointer user_data,
    GDestroyNotify user_data_free_func);

/**
 * ful_url_launcher_api_clear_method_handlers:
 *
 * @messenger: an #FlBinaryMessenger.
 * @suffix: (allow-none): a suffix to add to the API or %NULL for none.
 *
 * Clears the method handlers in the UrlLauncherApi API.
 */
void ful_url_launcher_api_clear_method_handlers(FlBinaryMessenger* messenger,
                                                const gchar* suffix);

G_END_DECLS

#endif  // PIGEON_MESSAGES_G_H_
