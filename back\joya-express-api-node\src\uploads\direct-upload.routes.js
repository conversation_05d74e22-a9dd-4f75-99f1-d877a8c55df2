const express = require('express');
const { body } = require('express-validator');
const directUploadController = require('./direct-upload.controller');
const { authenticateAccessToken } = require('../middleware/auth.middleware');
const { authenticateConductorToken } = require('../middleware/conductor-auth.middleware');

const router = express.Router();

// Middleware para autenticación (usuarios o conductores)
const authenticateUser = (req, res, next) => {
    // Intentar autenticar como usuario primero
    authenticateAccessToken(req, res, (err) => {
        if (err || !req.user) {
            // Si falla, intentar como conductor
            authenticateConductorToken(req, res, (err2) => {
                if (err2 || !req.user) {
                    return res.status(401).json({
                        success: false,
                        message: 'Token de autenticación requerido'
                    });
                }
                next();
            });
        } else {
            next();
        }
    });
};

// Validaciones para upload directo
const directUploadValidation = [
    body('folder')
        .optional()
        .isIn(['profile', 'vehicle', 'document', 'general'])
        .withMessage('Carpeta no válida. Valores permitidos: profile, vehicle, document, general')
];

// POST /api/uploads/direct - Subir imagen directamente
router.post('/direct', 
    authenticateUser,
    directUploadController.constructor.getUploadMiddleware(),
    directUploadValidation,
    directUploadController.uploadImage.bind(directUploadController)
);

// DELETE /api/uploads/direct/:key - Eliminar imagen
router.delete('/direct/:key', 
    authenticateUser,
    directUploadController.deleteImage.bind(directUploadController)
);

module.exports = router;
